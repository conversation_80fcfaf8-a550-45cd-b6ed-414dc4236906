# GPT Plus Bot Configuration
# Copy this file to .env and fill in your actual values

# Authentication credentials
GPTPLUS_EMAIL=<EMAIL>
GPTPLUS_PASSWORD=your_secure_password

# Purchase settings
DEFAULT_QUANTITY=1
MAX_QUANTITY=5
PREFERRED_PRODUCT=GPT-PLUS

# Bot behavior
DRY_RUN=true
REQUEST_DELAY=2.0
MAX_RETRIES=3
TIMEOUT=30

# Logging
LOG_LEVEL=INFO
LOG_FILE=bot.log

# Safety features
ENABLE_PURCHASE_CONFIRMATION=true
MAX_DAILY_PURCHASES=10
