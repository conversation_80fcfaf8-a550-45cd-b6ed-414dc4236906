"""
Main bot orchestration module for the GPT Plus purchase bot.
Coordinates all modules and provides the core bot functionality.
"""

import time
import logging
import signal
import sys
from typing import Optional, Dict, Any
from contextlib import contextmanager
from datetime import datetime

from .config import BotConfig, get_config, setup_logging
from .auth import SessionManager, AuthenticationError
from .products import ProductManager, ProductDiscoveryError
from .purchase import PurchaseManager, PurchaseError
from .monitoring import PurchaseMonitor, ErrorTracker, PerformanceMonitor
from .safety import RateLimiter, PurchaseSafetyGuard, RetryManager, ConfirmationManager, ToSCompliance


class GPTPlusPurchaseBot:
    """Main bot class that orchestrates all purchasing operations."""
    
    def __init__(self, config: Optional[BotConfig] = None):
        # Load configuration
        self.config = config or get_config()
        
        # Set up logging
        self.logger = setup_logging(self.config)
        self.logger.info("Initializing GPT Plus Purchase Bot")
        
        # Initialize core components
        self.session_manager = SessionManager(self.config)
        self.product_manager = ProductManager(self.config, self.session_manager)
        self.purchase_manager = PurchaseManager(self.config, self.session_manager, self.product_manager)
        
        # Initialize monitoring and safety components
        self.purchase_monitor = PurchaseMonitor(self.config)
        self.error_tracker = ErrorTracker(self.config)
        self.performance_monitor = PerformanceMonitor(self.config)
        self.rate_limiter = RateLimiter(self.config)
        self.safety_guard = PurchaseSafetyGuard(self.config)
        self.retry_manager = RetryManager(self.config)
        self.confirmation_manager = ConfirmationManager(self.config)
        self.tos_compliance = ToSCompliance(self.config)
        
        # Bot state
        self.is_running = False
        self.is_authenticated = False
        
        # Set up signal handlers for graceful shutdown (only in main thread)
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except ValueError:
            # Signal handlers can only be set in the main thread
            # This is expected when running in a web server or thread
            self.logger.debug("Signal handlers not set (not in main thread)")
        
        # Check ToS compliance
        self.tos_compliance.log_compliance_status()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.shutdown()
        sys.exit(0)
    
    def start(self) -> bool:
        """
        Start the bot and perform initial setup.
        
        Returns:
            True if startup successful, False otherwise
        """
        try:
            self.logger.info("Starting GPT Plus Purchase Bot")
            self.is_running = True
            
            # Authenticate with the website
            if not self.authenticate():
                self.logger.error("Authentication failed, cannot start bot")
                return False
            
            # Discover available products
            self.logger.info("Discovering available products...")
            products = self.product_manager.discover_products()
            self.logger.info(f"Found {len(products)} products available")
            
            # Display available products
            self._display_products(products)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            self.error_tracker.record_error("startup_error", str(e))
            return False
    
    def authenticate(self) -> bool:
        """
        Authenticate with the website.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            self.logger.info("Authenticating with website...")
            
            # Rate limiting for login attempts
            self.rate_limiter.wait_if_needed('login')
            
            # Attempt login with retry logic
            def login_operation():
                return self.session_manager.login(self.config.email, self.config.password)
            
            success = self.retry_manager.execute_with_retry(
                login_operation, "login"
            )
            
            if success:
                self.is_authenticated = True
                self.rate_limiter.record_request('login')
                self.logger.info("Authentication successful")
                return True
            else:
                self.logger.error("Authentication failed")
                return False
                
        except AuthenticationError as e:
            self.logger.error(f"Authentication error: {e}")
            self.error_tracker.record_error("authentication_error", str(e))
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during authentication: {e}")
            self.error_tracker.record_error("unexpected_auth_error", str(e))
            return False
    
    def purchase_product(self, product_name: str = None, quantity: int = None) -> bool:
        """
        Purchase a product with the specified parameters.
        
        Args:
            product_name: Name of product to purchase (None for auto-select)
            quantity: Quantity to purchase (None for default)
            
        Returns:
            True if purchase successful, False otherwise
        """
        try:
            if not self.is_authenticated:
                self.logger.error("Not authenticated, cannot make purchase")
                return False
            
            # Use default quantity if not specified
            if quantity is None:
                quantity = self.config.default_quantity
            
            # Check if purchase is allowed by safety guard
            # We'll use a default price for safety check, will be updated with actual price
            can_purchase, reason = self.safety_guard.can_purchase(quantity, 100.0)
            if not can_purchase:
                self.logger.error(f"Purchase blocked by safety guard: {reason}")
                return False
            
            # Check daily purchase limits
            if not self.purchase_monitor.check_daily_limit():
                self.logger.error("Daily purchase limit reached")
                return False
            
            # Discover products and select target
            self.rate_limiter.wait_if_needed('product_discovery')
            products = self.product_manager.discover_products()
            self.rate_limiter.record_request('product_discovery')
            
            # Select product
            if product_name:
                product = self.product_manager.find_product_by_name(product_name)
                if not product:
                    self.logger.error(f"Product not found: {product_name}")
                    return False
            else:
                product = self.product_manager.select_best_product(self.config.preferred_product)
                if not product:
                    self.logger.error("No suitable product found")
                    return False
            
            self.logger.info(f"Selected product: {product}")
            
            # Check product availability
            if not product.is_available or product.stock < quantity:
                self.logger.error(f"Product not available or insufficient stock: {product.stock} < {quantity}")
                return False
            
            # Final safety check with actual price
            total_cost = product.price * quantity
            can_purchase, reason = self.safety_guard.can_purchase(quantity, product.price)
            if not can_purchase:
                self.logger.error(f"Purchase blocked by safety guard: {reason}")
                return False
            
            # Get user confirmation if required
            if not self.confirmation_manager.confirm_purchase(
                product.name, quantity, total_cost, product.currency
            ):
                self.logger.info("Purchase cancelled by user")
                return False
            
            # Execute purchase with rate limiting and retry logic
            self.rate_limiter.wait_if_needed('purchase')
            
            start_time = time.time()
            
            def purchase_operation():
                return self.purchase_manager.purchase_product(product, quantity)
            
            result = self.retry_manager.execute_with_retry(
                purchase_operation, "purchase"
            )
            
            # Record performance metrics
            operation_time = time.time() - start_time
            self.performance_monitor.record_operation_time("purchase", operation_time)
            self.rate_limiter.record_request('purchase')
            
            # Record purchase attempt
            self.safety_guard.record_purchase_attempt(quantity, product.price, result.success)
            self.purchase_monitor.record_purchase(result, product.name, quantity)
            
            if result.success:
                self.logger.info(f"Purchase successful! Order ID: {result.order_id}")
                if result.product_codes:
                    self.logger.info(f"Product codes: {', '.join(result.product_codes)}")
                return True
            else:
                self.logger.error(f"Purchase failed: {result.error_message}")
                self.error_tracker.record_error("purchase_failed", result.error_message or "Unknown error")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during purchase: {e}")
            self.error_tracker.record_error("purchase_error", str(e))
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status."""
        return {
            'bot_status': {
                'running': self.is_running,
                'authenticated': self.is_authenticated,
                'config': {
                    'dry_run': self.config.dry_run,
                    'email': self.config.email,
                    'preferred_product': self.config.preferred_product
                }
            },
            'safety_status': self.safety_guard.get_safety_status(),
            'rate_limit_status': self.rate_limiter.get_rate_limit_status(),
            'session_summary': self.purchase_monitor.get_session_summary(),
            'error_summary': self.error_tracker.get_error_summary(),
            'performance_summary': self.performance_monitor.get_performance_summary()
        }
    
    def _display_products(self, products):
        """Display available products in a formatted way."""
        if not products:
            self.logger.info("No products available")
            return
        
        self.logger.info("Available products:")
        for product in products:
            availability = "✓ Available" if product.is_available else "✗ Out of stock"
            self.logger.info(f"  - {product.name}: {product.currency}{product.price} (Stock: {product.stock}) [{availability}]")
    
    @contextmanager
    def operation_context(self, operation_name: str):
        """Context manager for tracking operations."""
        start_time = time.time()
        self.logger.debug(f"Starting operation: {operation_name}")
        
        try:
            yield
        except Exception as e:
            self.error_tracker.record_error(f"{operation_name}_error", str(e))
            raise
        finally:
            duration = time.time() - start_time
            self.performance_monitor.record_operation_time(operation_name, duration)
            self.logger.debug(f"Completed operation: {operation_name} ({duration:.2f}s)")
    
    def shutdown(self):
        """Shutdown the bot gracefully."""
        self.logger.info("Shutting down bot...")
        self.is_running = False
        
        try:
            # Log session summary
            summary = self.purchase_monitor.get_session_summary()
            self.logger.info(f"Session summary: {summary}")
            
            # Logout if authenticated
            if self.is_authenticated:
                self.session_manager.logout()
                self.is_authenticated = False
            
            # Close session
            self.session_manager.close()
            
            self.logger.info("Bot shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.shutdown()

    def run_interactive_mode(self):
        """Run the bot in interactive mode."""
        self.logger.info("Starting interactive mode...")

        while self.is_running:
            try:
                print("\n" + "="*50)
                print("GPT Plus Purchase Bot - Interactive Mode")
                print("="*50)
                print("1. Purchase product")
                print("2. View available products")
                print("3. Check status")
                print("4. View purchase history")
                print("5. Exit")
                print("="*50)

                choice = input("Select option (1-5): ").strip()

                if choice == '1':
                    self._interactive_purchase()
                elif choice == '2':
                    self._interactive_view_products()
                elif choice == '3':
                    self._interactive_status()
                elif choice == '4':
                    self._interactive_history()
                elif choice == '5':
                    break
                else:
                    print("Invalid option. Please select 1-5.")

            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                self.logger.error(f"Error in interactive mode: {e}")
                print(f"Error: {e}")

        self.shutdown()

    def _interactive_purchase(self):
        """Handle interactive purchase."""
        try:
            # Get available products
            products = self.product_manager.discover_products()
            available_products = [p for p in products if p.is_available]

            if not available_products:
                print("No products available for purchase.")
                return

            # Display products
            print("\nAvailable products:")
            for i, product in enumerate(available_products, 1):
                print(f"{i}. {product.name} - {product.currency}{product.price} (Stock: {product.stock})")

            # Get user selection
            while True:
                try:
                    selection = input(f"\nSelect product (1-{len(available_products)}) or 'cancel': ").strip()
                    if selection.lower() == 'cancel':
                        return

                    product_index = int(selection) - 1
                    if 0 <= product_index < len(available_products):
                        selected_product = available_products[product_index]
                        break
                    else:
                        print("Invalid selection.")
                except ValueError:
                    print("Please enter a valid number.")

            # Get quantity
            while True:
                try:
                    quantity_input = input(f"Enter quantity (1-{min(selected_product.stock, self.config.max_quantity)}): ").strip()
                    quantity = int(quantity_input)
                    if 1 <= quantity <= min(selected_product.stock, self.config.max_quantity):
                        break
                    else:
                        print(f"Quantity must be between 1 and {min(selected_product.stock, self.config.max_quantity)}")
                except ValueError:
                    print("Please enter a valid number.")

            # Execute purchase
            success = self.purchase_product(selected_product.name, quantity)
            if success:
                print("Purchase completed successfully!")
            else:
                print("Purchase failed. Check logs for details.")

        except Exception as e:
            print(f"Error during purchase: {e}")

    def _interactive_view_products(self):
        """Display products in interactive mode."""
        try:
            products = self.product_manager.discover_products()
            if not products:
                print("No products found.")
                return

            print("\nAll products:")
            for product in products:
                status = "Available" if product.is_available else "Out of stock"
                print(f"- {product.name}")
                print(f"  Price: {product.currency}{product.price}")
                print(f"  Stock: {product.stock}")
                print(f"  Status: {status}")
                print(f"  Delivery: {product.delivery_method}")
                print()

        except Exception as e:
            print(f"Error retrieving products: {e}")

    def _interactive_status(self):
        """Display bot status in interactive mode."""
        try:
            status = self.get_status()

            print("\nBot Status:")
            print(f"Running: {status['bot_status']['running']}")
            print(f"Authenticated: {status['bot_status']['authenticated']}")
            print(f"Dry Run Mode: {status['bot_status']['config']['dry_run']}")

            print("\nSafety Status:")
            safety = status['safety_status']
            print(f"Daily purchases: {safety['daily_purchases']}/{safety['max_daily_purchases']}")
            print(f"Session purchases: {safety['session_purchases']}/{safety['max_session_purchases']}")
            print(f"Total spent: ${safety['total_spent']:.2f}")

            print("\nSession Summary:")
            session = status['session_summary']
            print(f"Uptime: {session.get('session_duration_minutes', 0):.1f} minutes")
            print(f"Purchase attempts: {session['total_purchases_attempted']}")
            print(f"Successful: {session['successful_purchases']}")
            print(f"Failed: {session['failed_purchases']}")

        except Exception as e:
            print(f"Error retrieving status: {e}")

    def _interactive_history(self):
        """Display purchase history in interactive mode."""
        try:
            history = self.purchase_monitor.get_purchase_history(days=7)

            if not history:
                print("No purchase history found.")
                return

            print("\nPurchase History (Last 7 days):")
            for record in history[-10:]:  # Show last 10 records
                timestamp = datetime.fromtimestamp(record['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                status = "SUCCESS" if record['success'] else "FAILED"
                dry_run = " (DRY RUN)" if record.get('dry_run', False) else ""

                print(f"{timestamp} - {record['product_name']} x{record['quantity']} - {status}{dry_run}")
                if record.get('order_id'):
                    print(f"  Order ID: {record['order_id']}")
                if record.get('error_message'):
                    print(f"  Error: {record['error_message']}")
                print()

        except Exception as e:
            print(f"Error retrieving history: {e}")
