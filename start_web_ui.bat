@echo off
chcp 65001 >nul
title GPT Plus 购买机器人 Web 界面

echo.
echo ========================================
echo   GPT Plus 购买机器人 Web 界面
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 显示Python版本
echo 🐍 Python版本:
python --version

REM 检查必要文件
if not exist "web_app.py" (
    echo ❌ 未找到 web_app.py 文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

echo.
echo 🚀 正在启动Web界面...
echo 📱 访问地址: http://localhost:5000
echo 🔑 默认登录: admin / admin123
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 启动Web应用
python start_web_ui.py

echo.
echo 👋 Web界面已停止
pause
