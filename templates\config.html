{% extends "base.html" %}

{% block title %}配置 - GPT Plus 购买机器人{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-cog me-2"></i>机器人配置
        </h1>
        <p class="text-muted">管理机器人的运行参数和安全设置</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-sliders-h me-2"></i>基本配置
            </div>
            <div class="card-body">
                <form id="config-form">
                    <!-- Authentication Settings -->
                    <h5 class="mb-3">
                        <i class="fas fa-user-lock me-2"></i>认证设置
                    </h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ config.email or '' }}" required>
                            <div class="form-text">用于登录 gptpluscz.com 的邮箱</div>
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="输入新密码以更改">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                            <div class="form-text">留空表示不更改当前密码</div>
                        </div>
                    </div>

                    <hr>

                    <!-- Purchase Settings -->
                    <h5 class="mb-3">
                        <i class="fas fa-shopping-cart me-2"></i>购买设置
                    </h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="preferred_product" class="form-label">首选产品</label>
                            <select class="form-select" id="preferred_product" name="preferred_product">
                                <option value="">自动选择</option>
                                <option value="GPT-PLUS" {% if config.preferred_product == 'GPT-PLUS' %}selected{% endif %}>GPT-PLUS</option>
                                <option value="GPT-GO" {% if config.preferred_product == 'GPT-GO' %}selected{% endif %}>GPT-GO</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="default_quantity" class="form-label">默认数量</label>
                            <input type="number" class="form-control" id="default_quantity" name="default_quantity" 
                                   min="1" max="10" value="{{ config.default_quantity or 1 }}">
                        </div>
                        <div class="col-md-4">
                            <label for="max_quantity" class="form-label">最大数量</label>
                            <input type="number" class="form-control" id="max_quantity" name="max_quantity" 
                                   min="1" max="20" value="{{ config.max_quantity or 5 }}">
                        </div>
                    </div>

                    <hr>

                    <!-- Safety Settings -->
                    <h5 class="mb-3">
                        <i class="fas fa-shield-alt me-2"></i>安全设置
                    </h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="dry_run" name="dry_run" 
                                       {% if config.dry_run %}checked{% endif %}>
                                <label class="form-check-label" for="dry_run">
                                    <strong>测试模式 (推荐)</strong>
                                </label>
                                <div class="form-text">启用后不会进行真实购买，仅用于测试</div>
                            </div>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enable_purchase_confirmation" 
                                       name="enable_purchase_confirmation" 
                                       {% if config.enable_purchase_confirmation %}checked{% endif %}>
                                <label class="form-check-label" for="enable_purchase_confirmation">
                                    购买确认
                                </label>
                                <div class="form-text">购买前需要用户确认</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="max_daily_purchases" class="form-label">每日购买限额</label>
                            <input type="number" class="form-control" id="max_daily_purchases" name="max_daily_purchases" 
                                   min="1" max="100" value="{{ config.max_daily_purchases or 10 }}">
                            <div class="form-text">防止意外过度购买</div>
                        </div>
                    </div>

                    <hr>

                    <!-- Performance Settings -->
                    <h5 class="mb-3">
                        <i class="fas fa-tachometer-alt me-2"></i>性能设置
                    </h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="request_delay" class="form-label">请求延迟 (秒)</label>
                            <input type="number" class="form-control" id="request_delay" name="request_delay" 
                                   min="0.5" max="10" step="0.5" value="{{ config.request_delay or 2.0 }}">
                            <div class="form-text">请求之间的延迟时间</div>
                        </div>
                        <div class="col-md-4">
                            <label for="max_retries" class="form-label">最大重试次数</label>
                            <input type="number" class="form-control" id="max_retries" name="max_retries" 
                                   min="1" max="10" value="{{ config.max_retries or 3 }}">
                        </div>
                        <div class="col-md-4">
                            <label for="timeout" class="form-label">超时时间 (秒)</label>
                            <input type="number" class="form-control" id="timeout" name="timeout" 
                                   min="10" max="120" value="{{ config.timeout or 30 }}">
                        </div>
                    </div>

                    <hr>

                    <!-- Logging Settings -->
                    <h5 class="mb-3">
                        <i class="fas fa-file-alt me-2"></i>日志设置
                    </h5>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="log_level" class="form-label">日志级别</label>
                            <select class="form-select" id="log_level" name="log_level">
                                <option value="DEBUG" {% if config.log_level == 'DEBUG' %}selected{% endif %}>DEBUG</option>
                                <option value="INFO" {% if config.log_level == 'INFO' %}selected{% endif %}>INFO</option>
                                <option value="WARNING" {% if config.log_level == 'WARNING' %}selected{% endif %}>WARNING</option>
                                <option value="ERROR" {% if config.log_level == 'ERROR' %}selected{% endif %}>ERROR</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="log_file" class="form-label">日志文件</label>
                            <input type="text" class="form-control" id="log_file" name="log_file" 
                                   value="{{ config.log_file or 'bot.log' }}">
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetConfig()">
                            <i class="fas fa-undo me-1"></i>重置为默认
                        </button>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="testConfig()">
                                <i class="fas fa-vial me-1"></i>测试配置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Configuration Status -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i>配置状态
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <span>配置文件已加载</span>
                </div>
                <div class="d-flex align-items-center mb-3">
                    <i class="fas fa-{% if config.dry_run %}flask text-warning{% else %}exclamation-triangle text-danger{% endif %} me-2"></i>
                    <span>{% if config.dry_run %}测试模式已启用{% else %}实际购买模式{% endif %}</span>
                </div>
                <div class="d-flex align-items-center">
                    <i class="fas fa-shield-alt text-primary me-2"></i>
                    <span>安全限制已配置</span>
                </div>
            </div>
        </div>

        <!-- Safety Warning -->
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-exclamation-triangle me-2"></i>安全提醒
            </div>
            <div class="card-body">
                <ul class="mb-0 small">
                    <li>首次使用请保持测试模式开启</li>
                    <li>确认配置正确后再关闭测试模式</li>
                    <li>设置合理的购买限额</li>
                    <li>定期检查购买历史</li>
                    <li>妥善保管登录凭据</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('password-toggle-icon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordField.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    function resetConfig() {
        if (confirm('确定要重置为默认配置吗？这将清除所有自定义设置。')) {
            // Reset form to default values
            document.getElementById('config-form').reset();
            
            // Set specific default values
            document.getElementById('default_quantity').value = 1;
            document.getElementById('max_quantity').value = 5;
            document.getElementById('max_daily_purchases').value = 10;
            document.getElementById('request_delay').value = 2.0;
            document.getElementById('max_retries').value = 3;
            document.getElementById('timeout').value = 30;
            document.getElementById('log_level').value = 'INFO';
            document.getElementById('log_file').value = 'bot.log';
            document.getElementById('dry_run').checked = true;
            document.getElementById('enable_purchase_confirmation').checked = true;
            
            showAlert('info', '配置已重置为默认值');
        }
    }

    function testConfig() {
        const formData = new FormData(document.getElementById('config-form'));
        const config = Object.fromEntries(formData);
        
        // Basic validation
        if (!config.email) {
            showAlert('warning', '请输入邮箱地址');
            return;
        }
        
        if (!config.email.includes('@')) {
            showAlert('warning', '请输入有效的邮箱地址');
            return;
        }
        
        if (parseInt(config.default_quantity) > parseInt(config.max_quantity)) {
            showAlert('warning', '默认数量不能大于最大数量');
            return;
        }
        
        showAlert('success', '配置验证通过！');
    }

    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const config = Object.fromEntries(formData);
        
        // Convert checkboxes to boolean
        config.dry_run = document.getElementById('dry_run').checked;
        config.enable_purchase_confirmation = document.getElementById('enable_purchase_confirmation').checked;
        
        // Convert numbers
        config.default_quantity = parseInt(config.default_quantity);
        config.max_quantity = parseInt(config.max_quantity);
        config.max_daily_purchases = parseInt(config.max_daily_purchases);
        config.request_delay = parseFloat(config.request_delay);
        config.max_retries = parseInt(config.max_retries);
        config.timeout = parseInt(config.timeout);
        
        // Save configuration
        fetch('/api/save_config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', data.error);
            } else {
                showAlert('success', '配置保存成功！');
            }
        })
        .catch(error => {
            showAlert('danger', '保存失败: ' + error.message);
        });
    });

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid.py-4');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Validate form inputs in real-time
    document.getElementById('default_quantity').addEventListener('input', function() {
        const maxQuantity = parseInt(document.getElementById('max_quantity').value);
        if (parseInt(this.value) > maxQuantity) {
            this.setCustomValidity('默认数量不能大于最大数量');
        } else {
            this.setCustomValidity('');
        }
    });

    document.getElementById('max_quantity').addEventListener('input', function() {
        const defaultQuantity = parseInt(document.getElementById('default_quantity').value);
        if (defaultQuantity > parseInt(this.value)) {
            document.getElementById('default_quantity').setCustomValidity('默认数量不能大于最大数量');
        } else {
            document.getElementById('default_quantity').setCustomValidity('');
        }
    });
</script>
{% endblock %}
