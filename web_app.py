#!/usr/bin/env python3
"""
Web UI for the GPT Plus Purchase Bot.
Provides a modern web interface for controlling and monitoring the bot.
"""

import os
import sys
import json
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_socketio import Socket<PERSON>, emit
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
import logging

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.bot import GPTPlusPurchaseBot
    from src.config import get_config, config_manager
    from src.monitoring import PurchaseMonitor
except ImportError as e:
    print(f"Warning: Could not import bot modules: {e}")
    print("Some features may not work properly.")

    # Create dummy functions for development
    def get_config():
        class DummyConfig:
            def __init__(self):
                self.email = "<EMAIL>"
                self.dry_run = True
                self.preferred_product = "GPT-PLUS"
                self.default_quantity = 1
                self.max_quantity = 5
                self.max_daily_purchases = 10
                self.request_delay = 2.0
                self.max_retries = 3
                self.timeout = 30
                self.log_level = "INFO"
                self.log_file = "bot.log"
                self.enable_purchase_confirmation = True
        return DummyConfig()

    class DummyMonitor:
        def __init__(self, config):
            pass
        def get_daily_purchase_count(self):
            return 0
        def get_purchase_history(self, days=7):
            return []

    PurchaseMonitor = DummyMonitor

    class DummyConfigManager:
        def save_credentials(self, email, password):
            pass
        def save_config(self, config):
            pass

    config_manager = DummyConfigManager()

    class DummyBot:
        def __init__(self, config):
            self.config = config
        def start(self):
            return True
        def shutdown(self):
            pass
        def get_status(self):
            return {"status": "dummy"}
        def purchase_product(self, product, quantity):
            return True

    GPTPlusPurchaseBot = DummyBot

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'gptplus_bot_secret_key_change_in_production'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Global bot instance
bot_instance = None
bot_thread = None
bot_running = False

# Simple user class for authentication
class User(UserMixin):
    def __init__(self, id):
        self.id = id

@login_manager.user_loader
def load_user(user_id):
    return User(user_id)

# Configure logging for web app
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('web_app')

@app.route('/')
@login_required
def dashboard():
    """Main dashboard page."""
    try:
        config = get_config()
        
        # Get bot status
        status_data = {
            'bot_running': bot_running,
            'config': {
                'email': config.email,
                'dry_run': config.dry_run,
                'preferred_product': config.preferred_product,
                'default_quantity': config.default_quantity,
                'max_daily_purchases': config.max_daily_purchases
            }
        }
        
        # Get purchase monitor data
        monitor = PurchaseMonitor(config)
        daily_count = monitor.get_daily_purchase_count()
        recent_history = monitor.get_purchase_history(days=1)
        
        status_data.update({
            'daily_purchases': daily_count,
            'recent_purchases': len(recent_history),
            'success_rate': calculate_success_rate(recent_history)
        })
        
        return render_template('dashboard.html', status=status_data)
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        flash(f"Error loading dashboard: {e}", 'error')
        return render_template('dashboard.html', status={})

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page."""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # Simple authentication (in production, use proper password hashing)
        if username == 'admin' and password == 'admin123':
            user = User('admin')
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Logout user."""
    logout_user()
    return redirect(url_for('login'))

@app.route('/config')
@login_required
def config_page():
    """Configuration page."""
    try:
        config = get_config()
        return render_template('config.html', config=config)
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        flash(f"Error loading configuration: {e}", 'error')
        return render_template('config.html', config={})

@app.route('/history')
@login_required
def history_page():
    """Purchase history page."""
    try:
        config = get_config()
        monitor = PurchaseMonitor(config)
        
        days = request.args.get('days', 7, type=int)
        history = monitor.get_purchase_history(days=days)
        
        return render_template('history.html', history=history, days=days)
    except Exception as e:
        logger.error(f"Error loading history: {e}")
        flash(f"Error loading history: {e}", 'error')
        return render_template('history.html', history=[], days=7)

@app.route('/logs')
@login_required
def logs_page():
    """Logs page."""
    try:
        config = get_config()
        log_file = config.log_file
        
        # Read last 100 lines of log file
        logs = []
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                logs = lines[-100:]  # Last 100 lines
        
        return render_template('logs.html', logs=logs)
    except Exception as e:
        logger.error(f"Error loading logs: {e}")
        flash(f"Error loading logs: {e}", 'error')
        return render_template('logs.html', logs=[])

# API Routes
@app.route('/api/status')
@login_required
def api_status():
    """Get current bot status."""
    try:
        config = get_config()
        monitor = PurchaseMonitor(config)
        
        status = {
            'bot_running': bot_running,
            'config': {
                'email': config.email,
                'dry_run': config.dry_run,
                'preferred_product': config.preferred_product,
                'default_quantity': config.default_quantity,
                'max_daily_purchases': config.max_daily_purchases
            },
            'daily_purchases': monitor.get_daily_purchase_count(),
            'timestamp': datetime.now().isoformat()
        }
        
        if bot_instance:
            bot_status = bot_instance.get_status()
            status.update(bot_status)
        
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/start_bot', methods=['POST'])
@login_required
def api_start_bot():
    """Start the bot."""
    global bot_instance, bot_thread, bot_running
    
    try:
        if bot_running:
            return jsonify({'error': 'Bot is already running'}), 400
        
        config = get_config()
        bot_instance = GPTPlusPurchaseBot(config)
        
        def run_bot():
            global bot_running
            try:
                bot_running = True
                socketio.emit('bot_status', {'status': 'starting'})

                # Start bot in thread-safe way
                if bot_instance.start():
                    socketio.emit('bot_status', {'status': 'running'})
                    logger.info("Bot started successfully")
                else:
                    bot_running = False
                    socketio.emit('bot_status', {'status': 'failed'})
                    logger.error("Bot failed to start")
            except ValueError as e:
                if "signal only works in main thread" in str(e):
                    # This is expected when running in web server
                    logger.info("Bot started in web mode (signal handlers disabled)")
                    socketio.emit('bot_status', {'status': 'running'})
                else:
                    bot_running = False
                    socketio.emit('bot_status', {'status': 'error', 'message': str(e)})
                    logger.error(f"Bot error: {e}")
            except Exception as e:
                bot_running = False
                socketio.emit('bot_status', {'status': 'error', 'message': str(e)})
                logger.error(f"Bot error: {e}")
        
        bot_thread = threading.Thread(target=run_bot)
        bot_thread.daemon = True
        bot_thread.start()
        
        return jsonify({'message': 'Bot starting...'})
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stop_bot', methods=['POST'])
@login_required
def api_stop_bot():
    """Stop the bot."""
    global bot_instance, bot_running
    
    try:
        if not bot_running:
            return jsonify({'error': 'Bot is not running'}), 400
        
        bot_running = False
        if bot_instance:
            bot_instance.shutdown()
            bot_instance = None
        
        socketio.emit('bot_status', {'status': 'stopped'})
        return jsonify({'message': 'Bot stopped'})
    except Exception as e:
        logger.error(f"Error stopping bot: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/purchase', methods=['POST'])
@login_required
def api_purchase():
    """Execute a purchase."""
    global bot_instance
    
    try:
        if not bot_running or not bot_instance:
            return jsonify({'error': 'Bot is not running'}), 400
        
        data = request.get_json()
        product_name = data.get('product_name')
        quantity = data.get('quantity', 1)
        
        # Execute purchase in background
        def execute_purchase():
            try:
                result = bot_instance.purchase_product(product_name, quantity)
                socketio.emit('purchase_result', {
                    'success': result,
                    'product': product_name,
                    'quantity': quantity,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                socketio.emit('purchase_result', {
                    'success': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        purchase_thread = threading.Thread(target=execute_purchase)
        purchase_thread.daemon = True
        purchase_thread.start()
        
        return jsonify({'message': 'Purchase initiated'})
    except Exception as e:
        logger.error(f"Error executing purchase: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/products')
@login_required
def api_products():
    """Get available products."""
    try:
        if not bot_running or not bot_instance:
            return jsonify({'error': 'Bot is not running'}), 400

        products = bot_instance.product_manager.discover_products()
        product_list = []

        for product in products:
            product_list.append({
                'id': product.id,
                'name': product.name,
                'price': product.price,
                'currency': product.currency,
                'stock': product.stock,
                'is_available': product.is_available,
                'delivery_method': product.delivery_method
            })

        return jsonify({'products': product_list})
    except Exception as e:
        logger.error(f"Error getting products: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/save_config', methods=['POST'])
@login_required
def api_save_config():
    """Save configuration."""
    try:
        data = request.get_json()

        # Update configuration
        config = get_config()

        # Update fields
        if 'email' in data:
            config.email = data['email']
        if 'password' in data and data['password']:
            # Save password to keyring
            config_manager.save_credentials(data['email'], data['password'])
        if 'preferred_product' in data:
            config.preferred_product = data['preferred_product']
        if 'default_quantity' in data:
            config.default_quantity = data['default_quantity']
        if 'max_quantity' in data:
            config.max_quantity = data['max_quantity']
        if 'dry_run' in data:
            config.dry_run = data['dry_run']
        if 'max_daily_purchases' in data:
            config.max_daily_purchases = data['max_daily_purchases']
        if 'enable_purchase_confirmation' in data:
            config.enable_purchase_confirmation = data['enable_purchase_confirmation']
        if 'request_delay' in data:
            config.request_delay = data['request_delay']
        if 'max_retries' in data:
            config.max_retries = data['max_retries']
        if 'timeout' in data:
            config.timeout = data['timeout']
        if 'log_level' in data:
            config.log_level = data['log_level']
        if 'log_file' in data:
            config.log_file = data['log_file']

        # Save configuration to environment file
        config_manager.save_config(config)

        return jsonify({'message': 'Configuration saved successfully'})
    except Exception as e:
        logger.error(f"Error saving config: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/logs')
@login_required
def api_logs():
    """Get recent logs."""
    try:
        config = get_config()
        log_file = config.log_file

        logs = []
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                logs = [line.strip() for line in lines[-200:]]  # Last 200 lines

        return jsonify({'logs': logs})
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/download_logs')
@login_required
def api_download_logs():
    """Download log file."""
    try:
        config = get_config()
        log_file = config.log_file

        if os.path.exists(log_file):
            from flask import send_file
            return send_file(log_file, as_attachment=True, download_name=f'bot_logs_{datetime.now().strftime("%Y%m%d")}.log')
        else:
            return jsonify({'error': 'Log file not found'}), 404
    except Exception as e:
        logger.error(f"Error downloading logs: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/export_history')
@login_required
def api_export_history():
    """Export purchase history."""
    try:
        config = get_config()
        monitor = PurchaseMonitor(config)

        days = request.args.get('days', 7, type=int)
        history = monitor.get_purchase_history(days=days)

        # Create CSV content
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['Timestamp', 'Product', 'Quantity', 'Unit Price', 'Total Amount', 'Currency', 'Status', 'Mode', 'Order ID', 'Error'])

        # Write data
        for record in history:
            writer.writerow([
                record.get('timestamp', ''),
                record.get('product_name', ''),
                record.get('quantity', 0),
                record.get('unit_price', 0),
                record.get('total_amount', 0),
                record.get('currency', ''),
                'Success' if record.get('success') else 'Failed',
                'Test' if record.get('dry_run') else 'Real',
                record.get('order_id', ''),
                record.get('error_message', '')
            ])

        output.seek(0)

        from flask import Response
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=purchase_history_{datetime.now().strftime("%Y%m%d")}.csv'}
        )
    except Exception as e:
        logger.error(f"Error exporting history: {e}")
        return jsonify({'error': str(e)}), 500

# WebSocket events
@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    logger.info('Client connected')
    emit('connected', {'message': 'Connected to bot server'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    logger.info('Client disconnected')

def calculate_success_rate(history):
    """Calculate success rate from purchase history."""
    if not history:
        return 0
    
    successful = sum(1 for record in history if record.get('success', False))
    return round((successful / len(history)) * 100, 1)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("🌐 Starting GPT Plus Bot Web Interface...")
    print("📱 Access the interface at: http://localhost:5000")
    print("🔑 Default login: admin / admin123")
    print("⚠️  Change the default password in production!")
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
