"""
Purchase and checkout module for the GPT Plus purchase bot.
Handles cart operations, checkout process, and payment completion.
"""

import time
import logging
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
from dataclasses import dataclass

from .config import BotConfig
from .auth import SessionManager, AuthenticationError
from .products import Product, ProductManager


@dataclass
class PurchaseResult:
    """Represents the result of a purchase attempt."""
    success: bool
    order_id: Optional[str] = None
    transaction_id: Optional[str] = None
    total_amount: float = 0.0
    currency: str = "¥"
    product_codes: List[str] = None
    error_message: Optional[str] = None
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.product_codes is None:
            self.product_codes = []
        if self.timestamp == 0.0:
            self.timestamp = time.time()


class PurchaseError(Exception):
    """Raised when purchase operations fail."""
    pass


class PurchaseManager:
    """Manages the complete purchase flow from cart to checkout."""
    
    def __init__(self, config: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, session_manager: SessionManager, product_manager: ProductManager):
        self.config = config
        self.session_manager = session_manager
        self.product_manager = product_manager
        self.logger = logging.getLogger('gptplus_bot.purchase')
    
    def purchase_product(self, product: Product, quantity: int = 1) -> PurchaseResult:
        """
        Purchase a product with the specified quantity.
        
        Args:
            product: Product to purchase
            quantity: Quantity to purchase
            
        Returns:
            PurchaseResult with details of the purchase attempt
            
        Raises:
            PurchaseError: If purchase fails
            AuthenticationError: If not authenticated
        """
        self.logger.info(f"Starting purchase: {product.name} x{quantity}")
        
        # Validate purchase parameters
        if quantity <= 0:
            raise PurchaseError("Quantity must be greater than 0")
        
        if quantity > self.config.max_quantity:
            raise PurchaseError(f"Quantity {quantity} exceeds maximum allowed {self.config.max_quantity}")
        
        if not product.is_available:
            raise PurchaseError(f"Product {product.name} is not available")
        
        if quantity > product.stock:
            raise PurchaseError(f"Requested quantity {quantity} exceeds available stock {product.stock}")
        
        # Check if this is a dry run
        if self.config.dry_run:
            self.logger.info("DRY RUN MODE - No actual purchase will be made")
            return self._simulate_purchase(product, quantity)
        
        try:
            # Step 1: Add product to cart
            self.logger.info("Adding product to cart...")
            cart_success = self._add_to_cart(product, quantity)
            if not cart_success:
                raise PurchaseError("Failed to add product to cart")
            
            # Step 2: Proceed to checkout
            self.logger.info("Proceeding to checkout...")
            checkout_data = self._proceed_to_checkout()
            if not checkout_data:
                raise PurchaseError("Failed to proceed to checkout")
            
            # Step 3: Complete payment
            self.logger.info("Completing payment...")
            purchase_result = self._complete_payment(checkout_data, product, quantity)
            
            if purchase_result.success:
                self.logger.info(f"Purchase completed successfully! Order ID: {purchase_result.order_id}")
            else:
                self.logger.error(f"Purchase failed: {purchase_result.error_message}")
            
            return purchase_result
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.error(f"Error during purchase: {e}")
            return PurchaseResult(
                success=False,
                error_message=str(e)
            )
    
    def _add_to_cart(self, product: Product, quantity: int) -> bool:
        """
        Add a product to the shopping cart.
        
        Args:
            product: Product to add
            quantity: Quantity to add
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.session_manager.get_session()
            
            # For this website, we need to find the purchase/buy button for the product
            # Since it's an automatic delivery system, it might be a direct purchase
            
            # Get the main page to find the purchase form
            main_url = urljoin(self.config.base_url, '/')
            response = session.get(main_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for purchase forms or buttons related to this product
            product_rows = soup.find_all('tr', class_='products-row')
            
            for row in product_rows:
                row_text = row.get_text().lower()
                if product.name.lower() in row_text:
                    # Found the product row, look for purchase button/form
                    purchase_button = row.find(['button', 'a'], text=re.compile(r'购买|buy|purchase', re.I))
                    if purchase_button:
                        # Handle button click or form submission
                        if purchase_button.name == 'button':
                            # It's a button, look for parent form
                            form = purchase_button.find_parent('form')
                            if form:
                                return self._submit_purchase_form(form, quantity)
                        elif purchase_button.name == 'a':
                            # It's a link, follow it
                            href = purchase_button.get('href')
                            if href:
                                purchase_url = urljoin(self.config.base_url, href)
                                return self._handle_purchase_link(purchase_url, quantity)
            
            # If no specific purchase button found, this might be a direct purchase system
            # Try to find a general purchase form
            purchase_forms = soup.find_all('form')
            for form in purchase_forms:
                if any(keyword in form.get_text().lower() for keyword in ['purchase', 'buy', '购买']):
                    return self._submit_purchase_form(form, quantity)
            
            self.logger.warning("Could not find purchase mechanism for product")
            return False
            
        except Exception as e:
            self.logger.error(f"Error adding to cart: {e}")
            return False
    
    def _submit_purchase_form(self, form, quantity: int) -> bool:
        """Submit a purchase form with the specified quantity."""
        try:
            session = self.session_manager.get_session()
            
            # Extract form data
            form_action = form.get('action', '')
            form_method = form.get('method', 'POST').upper()
            
            if form_action.startswith('/'):
                form_url = urljoin(self.config.base_url, form_action)
            else:
                form_url = form_action or self.config.base_url
            
            # Build form data
            form_data = {}
            
            # Add all form inputs
            inputs = form.find_all('input')
            for input_elem in inputs:
                name = input_elem.get('name')
                if name:
                    input_type = input_elem.get('type', 'text').lower()
                    if input_type == 'hidden':
                        form_data[name] = input_elem.get('value', '')
                    elif name.lower() in ['quantity', 'qty', 'amount']:
                        form_data[name] = str(quantity)
                    elif input_type in ['text', 'number']:
                        form_data[name] = input_elem.get('value', '')
            
            # Add quantity if not already present
            if not any(key.lower() in ['quantity', 'qty', 'amount'] for key in form_data.keys()):
                form_data['quantity'] = str(quantity)
            
            # Add delay
            time.sleep(self.config.request_delay)
            
            # Submit form
            if form_method == 'GET':
                response = session.get(form_url, params=form_data)
            else:
                response = session.post(form_url, data=form_data)
            
            response.raise_for_status()
            
            # Check if submission was successful
            return self._check_cart_success(response)
            
        except Exception as e:
            self.logger.error(f"Error submitting purchase form: {e}")
            return False
    
    def _handle_purchase_link(self, purchase_url: str, quantity: int) -> bool:
        """Handle a purchase link with quantity parameter."""
        try:
            session = self.session_manager.get_session()
            
            # Add quantity parameter to URL if needed
            if 'quantity' not in purchase_url and 'qty' not in purchase_url:
                separator = '&' if '?' in purchase_url else '?'
                purchase_url += f"{separator}quantity={quantity}"
            
            # Add delay
            time.sleep(self.config.request_delay)
            
            # Follow the purchase link
            response = session.get(purchase_url)
            response.raise_for_status()
            
            return self._check_cart_success(response)
            
        except Exception as e:
            self.logger.error(f"Error handling purchase link: {e}")
            return False
    
    def _check_cart_success(self, response) -> bool:
        """Check if adding to cart was successful."""
        # Check for success indicators in the response
        success_indicators = [
            'success', 'added', 'cart', '成功', '已添加'
        ]
        
        error_indicators = [
            'error', 'failed', 'out of stock', '错误', '失败', '缺货'
        ]
        
        response_text = response.text.lower()
        
        # Check for error indicators first
        if any(indicator in response_text for indicator in error_indicators):
            return False
        
        # Check for success indicators
        if any(indicator in response_text for indicator in success_indicators):
            return True
        
        # If redirected to checkout or cart page, consider it success
        if any(keyword in response.url.lower() for keyword in ['checkout', 'cart', 'order']):
            return True
        
        # Default to success if no clear indicators
        return response.status_code == 200
    
    def _proceed_to_checkout(self) -> Optional[Dict[str, Any]]:
        """Proceed to checkout and extract checkout data."""
        try:
            session = self.session_manager.get_session()
            
            # Try common checkout URLs
            checkout_urls = ['/checkout', '/order', '/payment']
            
            for checkout_path in checkout_urls:
                checkout_url = urljoin(self.config.base_url, checkout_path)
                
                try:
                    response = session.get(checkout_url)
                    if response.status_code == 200:
                        return self._extract_checkout_data(response)
                except:
                    continue
            
            # If no standard checkout URL works, look for checkout forms on current page
            current_response = session.get(self.config.base_url)
            return self._extract_checkout_data(current_response)
            
        except Exception as e:
            self.logger.error(f"Error proceeding to checkout: {e}")
            return None
    
    def _extract_checkout_data(self, response) -> Dict[str, Any]:
        """Extract checkout data from the response."""
        soup = BeautifulSoup(response.text, 'html.parser')
        
        checkout_data = {
            'url': response.url,
            'forms': [],
            'total_amount': 0.0,
            'currency': '¥'
        }
        
        # Extract checkout forms
        forms = soup.find_all('form')
        for form in forms:
            form_data = {
                'action': form.get('action', ''),
                'method': form.get('method', 'POST'),
                'inputs': {}
            }
            
            # Extract form inputs
            inputs = form.find_all('input')
            for input_elem in inputs:
                name = input_elem.get('name')
                if name:
                    form_data['inputs'][name] = {
                        'type': input_elem.get('type', 'text'),
                        'value': input_elem.get('value', ''),
                        'required': input_elem.has_attr('required')
                    }
            
            checkout_data['forms'].append(form_data)
        
        # Extract total amount
        amount_text = soup.get_text()
        amount_match = re.search(r'[¥$€£]\s*(\d+(?:\.\d+)?)', amount_text)
        if amount_match:
            checkout_data['total_amount'] = float(amount_match.group(1))
            currency_match = re.search(r'([¥$€£])', amount_text)
            if currency_match:
                checkout_data['currency'] = currency_match.group(1)
        
        return checkout_data
    
    def _complete_payment(self, checkout_data: Dict[str, Any], product: Product, quantity: int) -> PurchaseResult:
        """Complete the payment process."""
        try:
            session = self.session_manager.get_session()
            
            # Find the payment form
            payment_form = None
            for form in checkout_data.get('forms', []):
                if any(keyword in str(form).lower() for keyword in ['payment', 'pay', 'order', '支付']):
                    payment_form = form
                    break
            
            if not payment_form and checkout_data.get('forms'):
                # Use the first form if no specific payment form found
                payment_form = checkout_data['forms'][0]
            
            if not payment_form:
                raise PurchaseError("No payment form found")
            
            # Prepare payment data
            payment_data = {}
            for name, input_info in payment_form['inputs'].items():
                if input_info['type'] == 'hidden':
                    payment_data[name] = input_info['value']
                elif name.lower() in ['quantity', 'qty']:
                    payment_data[name] = str(quantity)
            
            # Submit payment
            form_action = payment_form['action']
            if form_action.startswith('/'):
                payment_url = urljoin(self.config.base_url, form_action)
            else:
                payment_url = form_action or checkout_data['url']
            
            time.sleep(self.config.request_delay)
            
            if payment_form['method'].upper() == 'GET':
                response = session.get(payment_url, params=payment_data)
            else:
                response = session.post(payment_url, data=payment_data)
            
            response.raise_for_status()
            
            # Parse payment result
            return self._parse_payment_result(response, product, quantity, checkout_data)
            
        except Exception as e:
            self.logger.error(f"Error completing payment: {e}")
            return PurchaseResult(
                success=False,
                error_message=f"Payment failed: {e}"
            )
    
    def _parse_payment_result(self, response, product: Product, quantity: int, checkout_data: Dict[str, Any]) -> PurchaseResult:
        """Parse the payment result from the response."""
        soup = BeautifulSoup(response.text, 'html.parser')
        response_text = response.text.lower()
        
        # Check for success indicators
        success_indicators = ['success', 'completed', 'order', '成功', '完成', '订单']
        error_indicators = ['error', 'failed', 'declined', '错误', '失败', '拒绝']
        
        is_success = any(indicator in response_text for indicator in success_indicators)
        has_error = any(indicator in response_text for indicator in error_indicators)
        
        if has_error:
            is_success = False
        
        # Extract order ID
        order_id = None
        order_patterns = [
            r'order[:\s#]*([a-zA-Z0-9]+)',
            r'订单[:\s#]*([a-zA-Z0-9]+)',
            r'#([a-zA-Z0-9]{6,})'
        ]
        
        for pattern in order_patterns:
            match = re.search(pattern, response.text, re.I)
            if match:
                order_id = match.group(1)
                break
        
        # Extract product codes (for automatic delivery)
        product_codes = []
        code_patterns = [
            r'code[:\s]*([a-zA-Z0-9\-]+)',
            r'key[:\s]*([a-zA-Z0-9\-]+)',
            r'兑换码[:\s]*([a-zA-Z0-9\-]+)'
        ]
        
        for pattern in code_patterns:
            matches = re.findall(pattern, response.text, re.I)
            product_codes.extend(matches)
        
        return PurchaseResult(
            success=is_success,
            order_id=order_id,
            total_amount=checkout_data.get('total_amount', product.price * quantity),
            currency=checkout_data.get('currency', product.currency),
            product_codes=product_codes,
            error_message=None if is_success else "Payment processing failed"
        )
    
    def _simulate_purchase(self, product: Product, quantity: int) -> PurchaseResult:
        """Simulate a purchase for dry-run mode."""
        self.logger.info(f"SIMULATING purchase of {product.name} x{quantity}")
        
        # Simulate processing time
        time.sleep(2)
        
        return PurchaseResult(
            success=True,
            order_id=f"DRY_RUN_{int(time.time())}",
            total_amount=product.price * quantity,
            currency=product.currency,
            product_codes=[f"DRY_RUN_CODE_{i+1}" for i in range(quantity)],
            error_message=None
        )
