# GPT Plus Automated Purchase Bot

An automated purchasing bot for gptpluscz.com that handles authentication, product selection, and checkout processes with comprehensive safety features.

## 🚀 Features

- **🔐 Secure Authentication**: Login with email/password credentials stored securely in system keyring
- **⚙️ Configurable Purchasing**: Set desired quantities and product preferences
- **🛡️ Safety Features**: Rate limiting, over-purchase protection, and comprehensive error handling
- **📊 Comprehensive Logging**: Detailed logs of all operations, errors, and purchase history
- **🧪 Dry Run Mode**: Test the bot without making actual purchases
- **🤝 Respect for ToS**: Built-in delays and respectful request patterns
- **🔄 Retry Logic**: Intelligent retry mechanisms for transient failures
- **📈 Performance Monitoring**: Track request times and operation performance
- **🎯 Interactive Mode**: User-friendly interactive interface for manual control
- **📋 Purchase History**: Track and review all purchase attempts

## 📦 Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd GPTQG
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up configuration**
   ```bash
   python main.py setup
   ```

   Or manually create a `.env` file (see Configuration section)

## ⚙️ Configuration

### Quick Setup
Run the interactive setup:
```bash
python main.py setup
```

### Manual Configuration
Create a `.env` file in the project root:

```env
# Authentication credentials
GPTPLUS_EMAIL=<EMAIL>
GPTPLUS_PASSWORD=your_secure_password

# Purchase settings
DEFAULT_QUANTITY=1
MAX_QUANTITY=5
PREFERRED_PRODUCT=GPT-PLUS

# Bot behavior
DRY_RUN=true
REQUEST_DELAY=2.0
MAX_RETRIES=3
TIMEOUT=30

# Logging
LOG_LEVEL=INFO
LOG_FILE=bot.log

# Safety features
ENABLE_PURCHASE_CONFIRMATION=true
MAX_DAILY_PURCHASES=10
```

### Configuration Options

| Option | Description | Default | Required |
|--------|-------------|---------|----------|
| `GPTPLUS_EMAIL` | Login email address | - | ✅ |
| `GPTPLUS_PASSWORD` | Login password | - | ✅ |
| `DEFAULT_QUANTITY` | Default purchase quantity | 1 | ❌ |
| `MAX_QUANTITY` | Maximum quantity per purchase | 5 | ❌ |
| `PREFERRED_PRODUCT` | Preferred product type | GPT-PLUS | ❌ |
| `DRY_RUN` | Enable dry-run mode | true | ❌ |
| `REQUEST_DELAY` | Delay between requests (seconds) | 2.0 | ❌ |
| `MAX_RETRIES` | Maximum retry attempts | 3 | ❌ |
| `TIMEOUT` | Request timeout (seconds) | 30 | ❌ |
| `LOG_LEVEL` | Logging level | INFO | ❌ |
| `LOG_FILE` | Log file path | bot.log | ❌ |
| `ENABLE_PURCHASE_CONFIRMATION` | Require purchase confirmation | true | ❌ |
| `MAX_DAILY_PURCHASES` | Maximum purchases per day | 10 | ❌ |

## 🎮 Usage

### Command Line Interface

The bot provides several commands for different use cases:

#### Basic Usage
```bash
# Run in dry-run mode (safe, no real purchases)
python main.py run

# Run with actual purchases (⚠️ CAUTION!)
python main.py run --no-dry-run

# Interactive mode
python main.py run --interactive

# Purchase specific product
python main.py run --product "GPT-PLUS" --quantity 2
```

#### Monitoring Commands
```bash
# Check bot status and configuration
python main.py status

# View purchase history
python main.py history

# View history for specific period
python main.py history --days 30

# Test bot functionality
python main.py test
```

### Interactive Mode

For easier use, run the bot in interactive mode:

```bash
python main.py run --interactive
```

This provides a menu-driven interface where you can:
- Purchase products with guided selection
- View available products and their status
- Check bot status and limits
- Review purchase history
- Exit safely

### Example Workflows

#### First Time Setup
```bash
# 1. Set up configuration
python main.py setup

# 2. Test the bot
python main.py test

# 3. Check status
python main.py status

# 4. Run in interactive mode
python main.py run --interactive
```

#### Automated Purchase
```bash
# Purchase 1 GPT-PLUS code (dry run)
python main.py run --product "GPT-PLUS" --quantity 1

# Purchase 2 codes with real money (⚠️ CAUTION!)
python main.py run --product "GPT-PLUS" --quantity 2 --no-dry-run
```

#### Monitoring and History
```bash
# Check current status
python main.py status

# View recent purchases
python main.py history --days 7

# View all purchase history
python main.py history --days 30
```

## 🛡️ Safety Features

### Purchase Protection
- **Daily Limits**: Configurable maximum purchases per day
- **Session Limits**: Maximum purchases per bot session
- **Quantity Validation**: Prevents excessive single purchases
- **Confirmation Prompts**: User confirmation before real purchases
- **Dry Run Mode**: Test all functionality without spending money

### Rate Limiting
- **Request Delays**: Automatic delays between requests
- **Adaptive Throttling**: Slower requests when rate limits are approached
- **Respectful Patterns**: Mimics human browsing behavior

### Error Handling
- **Network Resilience**: Automatic retry for network failures
- **Authentication Recovery**: Re-authentication on session expiry
- **Graceful Degradation**: Continues operation despite minor errors
- **Comprehensive Logging**: Detailed error tracking and reporting

### Monitoring
- **Real-time Status**: Live monitoring of bot operations
- **Performance Metrics**: Track request times and success rates
- **Purchase History**: Complete audit trail of all activities
- **Error Analytics**: Pattern recognition for recurring issues

## 📊 Logging and Monitoring

### Log Files
- **Main Log**: `bot.log` - All bot operations and errors
- **Purchase Records**: `logs/monitoring/purchases_YYYY-MM-DD.json` - Daily purchase records
- **Performance Data**: Tracked in memory and available via status commands

### Log Levels
- **DEBUG**: Detailed debugging information
- **INFO**: General operational information
- **WARNING**: Important notices and recoverable errors
- **ERROR**: Error conditions that affect functionality
- **CRITICAL**: Severe errors that may stop the bot

### Monitoring Commands
```bash
# Real-time status
python main.py status

# Purchase history
python main.py history

# Test functionality
python main.py test
```

## ⚠️ Important Warnings

### Before Using This Bot

1. **🔴 REAL MONEY INVOLVED**: When dry-run mode is disabled, this bot makes real purchases with real money
2. **📋 TERMS OF SERVICE**: Ensure you have permission to use automated tools on the target website
3. **🏛️ LEGAL COMPLIANCE**: Verify compliance with local laws and regulations
4. **💰 FINANCIAL RESPONSIBILITY**: You are responsible for all purchases made by the bot
5. **🔒 CREDENTIAL SECURITY**: Keep your login credentials secure and never share them

### Recommended Practices

- **Always test in dry-run mode first**
- **Start with small quantities**
- **Monitor the bot during operation**
- **Review purchase history regularly**
- **Keep the bot updated**
- **Use strong, unique passwords**

## 🔧 Troubleshooting

### Common Issues

#### Authentication Failures
```bash
# Check credentials
python main.py status

# Test authentication
python main.py test

# Re-run setup if needed
python main.py setup
```

#### Network Issues
- Check internet connection
- Verify website accessibility
- Review rate limiting settings
- Check firewall/proxy settings

#### Purchase Failures
- Verify product availability
- Check account balance/payment method
- Review daily/session limits
- Check for website maintenance

#### Configuration Issues
```bash
# Validate configuration
python main.py status

# Re-run setup
python main.py setup

# Check log files for detailed errors
tail -f bot.log
```

### Getting Help

1. **Check Logs**: Review `bot.log` for detailed error information
2. **Run Tests**: Use `python main.py test` to diagnose issues
3. **Check Status**: Use `python main.py status` to verify configuration
4. **Review History**: Use `python main.py history` to check recent activities

## 📁 Project Structure

```
GPTQG/
├── main.py                 # CLI entry point
├── requirements.txt        # Python dependencies
├── .env.example           # Configuration template
├── README.md              # This documentation
├── src/                   # Source code
│   ├── __init__.py
│   ├── bot.py            # Main bot orchestration
│   ├── config.py         # Configuration management
│   ├── auth.py           # Authentication handling
│   ├── products.py       # Product discovery
│   ├── purchase.py       # Purchase operations
│   ├── monitoring.py     # Logging and monitoring
│   ├── safety.py         # Safety features
│   └── exceptions.py     # Custom exceptions
└── logs/                 # Log files (created automatically)
    └── monitoring/       # Purchase records
```

## 🤝 Contributing

This project is for educational purposes. If you find bugs or have suggestions:

1. Review the code thoroughly
2. Test any changes in dry-run mode
3. Ensure compliance with website terms of service
4. Document any modifications clearly

## ⚖️ Legal Notice

### Terms of Use

This bot is provided for **educational and research purposes only**. Users must:

- ✅ Comply with the target website's terms of service
- ✅ Respect rate limits and server resources
- ✅ Use the bot responsibly and ethically
- ✅ Ensure compliance with local laws and regulations
- ✅ Obtain proper authorization before use
- ✅ Take responsibility for all actions performed by the bot

### Prohibited Uses

- ❌ Commercial resale or redistribution
- ❌ Circumventing website security measures
- ❌ Violating terms of service
- ❌ Excessive or abusive usage patterns
- ❌ Any illegal activities

### Disclaimer

**THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND.** The authors and contributors:

- Are not liable for any damages or losses
- Do not guarantee functionality or availability
- Are not responsible for user actions or violations
- Provide no support for misuse or illegal activities

Users assume all risks and responsibilities when using this software.

## 📄 License

This project is provided for educational purposes. Users are responsible for ensuring their use complies with all applicable laws and terms of service.

---

**⚠️ Remember: Always test in dry-run mode first, and use responsibly!**
