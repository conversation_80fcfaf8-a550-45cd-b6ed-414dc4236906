#!/usr/bin/env python3
"""
Test suite for the GPT Plus Purchase Bot.
Validates functionality and safety features in dry-run mode.
"""

import sys
import os
import time
import tempfile
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import BotConfig
from src.bot import GPTPlusPurchaseBot
from src.monitoring import PurchaseMonitor
from src.safety import RateLimiter, PurchaseSafetyGuard


class BotTester:
    """Test suite for bot functionality."""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = tempfile.mkdtemp()
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log a test result."""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}"
        if message:
            result += f": {message}"
        
        print(result)
        self.test_results.append((test_name, passed, message))
        
        return passed
    
    def test_configuration(self):
        """Test configuration loading and validation."""
        print("\n=== Testing Configuration ===")
        
        try:
            # Test with minimal valid config
            config_data = {
                'email': '<EMAIL>',
                'password': 'test_password',
                'dry_run': True
            }
            
            config = BotConfig(**config_data)
            self.log_test("Configuration Creation", True, "Valid config created")
            
            # Test validation
            self.log_test("Email Validation", '@' in config.email, f"Email: {config.email}")
            self.log_test("Dry Run Default", config.dry_run == True, "Dry run enabled by default")
            self.log_test("Default Quantity", config.default_quantity == 1, f"Default quantity: {config.default_quantity}")
            
            return True
            
        except Exception as e:
            self.log_test("Configuration Creation", False, str(e))
            return False
    
    def test_safety_features(self):
        """Test safety features and limits."""
        print("\n=== Testing Safety Features ===")
        
        try:
            config = BotConfig(
                email='<EMAIL>',
                password='test_password',
                dry_run=True,
                max_quantity=5,
                max_daily_purchases=10
            )
            
            safety_guard = PurchaseSafetyGuard(config)
            
            # Test quantity limits
            can_purchase, reason = safety_guard.can_purchase(1, 100.0)
            self.log_test("Valid Quantity Check", can_purchase, "Quantity 1 allowed")
            
            can_purchase, reason = safety_guard.can_purchase(10, 100.0)
            self.log_test("Excessive Quantity Check", not can_purchase, f"Quantity 10 blocked: {reason}")
            
            can_purchase, reason = safety_guard.can_purchase(0, 100.0)
            self.log_test("Zero Quantity Check", not can_purchase, "Zero quantity blocked")
            
            # Test daily limits
            for i in range(12):  # Try to exceed daily limit
                safety_guard.record_purchase_attempt(1, 100.0, True)
            
            can_purchase, reason = safety_guard.can_purchase(1, 100.0)
            self.log_test("Daily Limit Check", not can_purchase, "Daily limit enforced")
            
            return True
            
        except Exception as e:
            self.log_test("Safety Features", False, str(e))
            return False
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        print("\n=== Testing Rate Limiting ===")
        
        try:
            config = BotConfig(
                email='<EMAIL>',
                password='test_password',
                request_delay=1.0
            )
            
            rate_limiter = RateLimiter(config)
            
            # Test initial request allowance
            can_request = rate_limiter.can_make_request('general')
            self.log_test("Initial Request Allowed", can_request, "First request allowed")
            
            # Record a request
            rate_limiter.record_request('general')
            
            # Test immediate second request (should be blocked by delay)
            can_request = rate_limiter.can_make_request('general')
            self.log_test("Rate Limit Enforcement", not can_request, "Second immediate request blocked")
            
            # Test different request types
            can_request = rate_limiter.can_make_request('login')
            self.log_test("Different Request Type", can_request, "Different request type allowed")
            
            return True
            
        except Exception as e:
            self.log_test("Rate Limiting", False, str(e))
            return False
    
    def test_monitoring(self):
        """Test monitoring and logging functionality."""
        print("\n=== Testing Monitoring ===")
        
        try:
            config = BotConfig(
                email='<EMAIL>',
                password='test_password',
                log_file=os.path.join(self.temp_dir, 'test.log')
            )
            
            monitor = PurchaseMonitor(config)
            
            # Test daily limit checking
            within_limit = monitor.check_daily_limit()
            self.log_test("Daily Limit Check", within_limit, "Within daily limits")
            
            # Test purchase count
            daily_count = monitor.get_daily_purchase_count()
            self.log_test("Daily Count Tracking", daily_count >= 0, f"Daily count: {daily_count}")
            
            # Test session summary
            summary = monitor.get_session_summary()
            self.log_test("Session Summary", 'start_time' in summary, "Session summary generated")
            
            return True
            
        except Exception as e:
            self.log_test("Monitoring", False, str(e))
            return False
    
    def test_dry_run_mode(self):
        """Test dry run mode functionality."""
        print("\n=== Testing Dry Run Mode ===")
        
        try:
            # Create a test config with dry run enabled
            config = BotConfig(
                email='<EMAIL>',
                password='test_password',
                dry_run=True,
                log_file=os.path.join(self.temp_dir, 'dry_run_test.log')
            )
            
            self.log_test("Dry Run Config", config.dry_run == True, "Dry run mode enabled")
            
            # Test that dry run is properly detected
            bot = GPTPlusPurchaseBot(config)
            self.log_test("Bot Dry Run Detection", bot.config.dry_run, "Bot recognizes dry run mode")
            
            return True
            
        except Exception as e:
            self.log_test("Dry Run Mode", False, str(e))
            return False
    
    def test_error_handling(self):
        """Test error handling and recovery."""
        print("\n=== Testing Error Handling ===")
        
        try:
            # Test invalid configuration
            try:
                invalid_config = BotConfig(
                    email='invalid_email',  # Invalid email format
                    password='test'
                )
                self.log_test("Invalid Email Handling", False, "Should have failed validation")
            except Exception:
                self.log_test("Invalid Email Handling", True, "Invalid email properly rejected")
            
            # Test missing required fields
            try:
                incomplete_config = BotConfig()
                self.log_test("Missing Required Fields", False, "Should have failed validation")
            except Exception:
                self.log_test("Missing Required Fields", True, "Missing fields properly rejected")
            
            return True
            
        except Exception as e:
            self.log_test("Error Handling", False, str(e))
            return False
    
    def test_file_operations(self):
        """Test file operations and logging."""
        print("\n=== Testing File Operations ===")
        
        try:
            config = BotConfig(
                email='<EMAIL>',
                password='test_password',
                log_file=os.path.join(self.temp_dir, 'file_test.log')
            )
            
            # Test log file creation
            from src.config import setup_logging
            logger = setup_logging(config)
            
            log_file = Path(config.log_file)
            self.log_test("Log File Creation", log_file.exists(), f"Log file: {log_file}")
            
            # Test log writing
            logger.info("Test log message")
            
            # Check if log was written
            if log_file.exists():
                with open(log_file, 'r') as f:
                    content = f.read()
                    has_test_message = "Test log message" in content
                    self.log_test("Log Writing", has_test_message, "Test message found in log")
            
            return True
            
        except Exception as e:
            self.log_test("File Operations", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all tests and return overall result."""
        print("🧪 Starting GPT Plus Purchase Bot Test Suite")
        print("=" * 60)
        
        tests = [
            self.test_configuration,
            self.test_safety_features,
            self.test_rate_limiting,
            self.test_monitoring,
            self.test_dry_run_mode,
            self.test_error_handling,
            self.test_file_operations
        ]
        
        all_passed = True
        for test in tests:
            try:
                result = test()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"Test failed with exception: {e}")
                all_passed = False
        
        # Print summary
        print("\n" + "=" * 60)
        print("🏁 Test Summary")
        print("=" * 60)
        
        passed_count = sum(1 for _, passed, _ in self.test_results if passed)
        total_count = len(self.test_results)
        
        print(f"Tests Passed: {passed_count}/{total_count}")
        
        if not all_passed:
            print("\n❌ Failed Tests:")
            for test_name, passed, message in self.test_results:
                if not passed:
                    print(f"  - {test_name}: {message}")
        
        if all_passed:
            print("\n✅ All tests passed! The bot appears to be working correctly.")
            print("🔒 Remember: This was tested in dry-run mode only.")
            print("⚠️  Always verify configuration before making real purchases.")
        else:
            print("\n❌ Some tests failed. Please review the issues above.")
            print("🔧 Fix any problems before using the bot.")
        
        return all_passed


def main():
    """Run the test suite."""
    tester = BotTester()
    success = tester.run_all_tests()
    
    # Clean up
    import shutil
    shutil.rmtree(tester.temp_dir, ignore_errors=True)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
