{% extends "base.html" %}

{% block title %}仪表板 - GPT Plus 购买机器人{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>仪表板
        </h1>
        <p class="text-muted">实时监控机器人状态和购买活动</p>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="metric-card">
            <div class="metric-value" id="daily-purchases">{{ status.daily_purchases or 0 }}</div>
            <div class="metric-label">今日购买</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="metric-card">
            <div class="metric-value">{{ status.recent_purchases or 0 }}</div>
            <div class="metric-label">最近购买</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="metric-card">
            <div class="metric-value">{{ status.success_rate or 0 }}%</div>
            <div class="metric-label">成功率</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="metric-card">
            <div class="metric-value">{{ status.config.max_daily_purchases or 0 }}</div>
            <div class="metric-label">每日限额</div>
        </div>
    </div>
</div>

<!-- Bot Control Panel -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-robot me-2"></i>机器人控制
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="me-3">状态:</span>
                            <div id="bot-status">
                                <span class="status-indicator {% if status.bot_running %}status-running{% else %}status-stopped{% endif %}"></span>
                                <span class="status-text">{% if status.bot_running %}运行中{% else %}已停止{% endif %}</span>
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <span class="me-3">模式:</span>
                            <span class="badge {% if status.config.dry_run %}bg-warning{% else %}bg-danger{% endif %}">
                                {% if status.config.dry_run %}
                                    <i class="fas fa-flask me-1"></i>测试模式
                                {% else %}
                                    <i class="fas fa-exclamation-triangle me-1"></i>实际购买
                                {% endif %}
                            </span>
                        </div>
                        
                        <div class="d-flex align-items-center">
                            <span class="me-3">账户:</span>
                            <span class="text-muted">{{ status.config.email or '未配置' }}</span>
                        </div>
                    </div>
                    
                    <div class="col-md-6 text-end">
                        <button id="start-bot" class="btn btn-success me-2" onclick="startBot()" 
                                {% if status.bot_running %}disabled{% endif %}>
                            <i class="fas fa-play me-1"></i>启动机器人
                        </button>
                        <button id="stop-bot" class="btn btn-danger" onclick="stopBot()" 
                                {% if not status.bot_running %}disabled{% endif %}>
                            <i class="fas fa-stop me-1"></i>停止机器人
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shopping-cart me-2"></i>快速购买
            </div>
            <div class="card-body">
                <form id="quick-purchase-form" onsubmit="quickPurchase(event)">
                    <div class="mb-3">
                        <label for="product-select" class="form-label">产品</label>
                        <select class="form-select" id="product-select" required>
                            <option value="">选择产品...</option>
                            <option value="GPT-PLUS">GPT-PLUS 兑换码</option>
                            <option value="GPT-GO">GPT-GO 兑换码</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity-input" class="form-label">数量</label>
                        <input type="number" class="form-control" id="quantity-input"
                               min="1" max="5" value="1" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100" 
                            {% if not status.bot_running %}disabled{% endif %}>
                        <i class="fas fa-shopping-cart me-1"></i>立即购买
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cog me-2"></i>当前配置
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">首选产品</small>
                        <div class="fw-bold">{{ status.config.preferred_product or '未设置' }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">默认数量</small>
                        <div class="fw-bold">{{ status.config.default_quantity or 1 }}</div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">每日限额</small>
                        <div class="fw-bold">{{ status.config.max_daily_purchases or 0 }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">运行模式</small>
                        <div class="fw-bold">
                            {% if status.config.dry_run %}测试模式{% else %}实际购买{% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('config_page') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>修改配置
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line me-2"></i>购买趋势
            </div>
            <div class="card-body">
                <canvas id="purchaseChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-clock me-2"></i>最近活动</span>
                <a href="{{ url_for('history_page') }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-history me-1"></i>查看全部
                </a>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p>暂无最近活动</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">处理中，请稍候...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize purchase trend chart
    const ctx = document.getElementById('purchaseChart').getContext('2d');
    const purchaseChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '购买次数',
                data: [2, 1, 3, 2, 4, 1, 2],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    function startBot() {
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        fetch('/api/start_bot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.hide();
            if (data.error) {
                showAlert('danger', data.error);
            } else {
                showAlert('success', data.message);
                document.getElementById('start-bot').disabled = true;
                document.getElementById('stop-bot').disabled = false;
            }
        })
        .catch(error => {
            loadingModal.hide();
            showAlert('danger', '启动失败: ' + error.message);
        });
    }

    function stopBot() {
        fetch('/api/stop_bot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', data.error);
            } else {
                showAlert('info', data.message);
                document.getElementById('start-bot').disabled = false;
                document.getElementById('stop-bot').disabled = true;
            }
        })
        .catch(error => {
            showAlert('danger', '停止失败: ' + error.message);
        });
    }

    function quickPurchase(event) {
        event.preventDefault();
        
        const product = document.getElementById('product-select').value;
        const quantity = parseInt(document.getElementById('quantity-input').value);
        
        if (!product) {
            showAlert('warning', '请选择产品');
            return;
        }
        
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        fetch('/api/purchase', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_name: product,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            loadingModal.hide();
            if (data.error) {
                showAlert('danger', data.error);
            } else {
                showAlert('info', data.message);
                // Reset form
                document.getElementById('quick-purchase-form').reset();
            }
        })
        .catch(error => {
            loadingModal.hide();
            showAlert('danger', '购买失败: ' + error.message);
        });
    }

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid.py-4');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Load available products
    function loadProducts() {
        fetch('/api/products')
            .then(response => response.json())
            .then(data => {
                if (data.products) {
                    const select = document.getElementById('product-select');
                    // Clear existing options except first
                    select.innerHTML = '<option value="">选择产品...</option>';
                    
                    data.products.forEach(product => {
                        if (product.is_available) {
                            const option = document.createElement('option');
                            option.value = product.name;
                            option.textContent = `${product.name} - ${product.currency}${product.price} (库存: ${product.stock})`;
                            select.appendChild(option);
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error loading products:', error);
            });
    }

    // Load products when bot is running
    if ({{ 'true' if status.bot_running else 'false' }}) {
        loadProducts();
    }
</script>
{% endblock %}
