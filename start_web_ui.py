#!/usr/bin/env python3
"""
启动脚本 - GPT Plus 购买机器人 Web 界面
简化的启动脚本，自动检查依赖并启动Web界面
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'flask',
        'flask-socketio', 
        'flask-login',
        'eventlet'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖"""
    print("🔧 正在安装缺失的依赖...")
    for package in packages:
        print(f"   安装 {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        except subprocess.CalledProcessError:
            print(f"❌ 安装 {package} 失败")
            return False
    return True

def start_web_interface():
    """启动Web界面"""
    print("🌐 启动 GPT Plus 购买机器人 Web 界面...")
    print("=" * 60)
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"⚠️  检测到缺失的依赖: {', '.join(missing)}")
        install_choice = input("是否自动安装? (y/n): ").lower().strip()
        
        if install_choice == 'y':
            if not install_dependencies(missing):
                print("❌ 依赖安装失败，请手动安装后重试")
                return False
        else:
            print("❌ 请手动安装依赖后重试")
            return False
    
    # 检查配置文件
    if not os.path.exists('.env'):
        print("⚠️  未找到配置文件 .env")
        create_config = input("是否创建示例配置文件? (y/n): ").lower().strip()
        
        if create_config == 'y':
            create_sample_config()
        else:
            print("💡 请确保配置文件存在后重新启动")
    
    # 启动Web应用
    try:
        print("\n🚀 启动Web界面...")
        print("📱 访问地址: http://localhost:5000")
        print("🔑 默认登录: admin / admin123")
        print("⚠️  生产环境请修改默认密码!")
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 导入并启动Web应用
        from web_app import app, socketio
        socketio.run(app, debug=False, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n👋 Web界面已停止")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return False

def create_sample_config():
    """创建示例配置文件"""
    sample_config = """# GPT Plus Bot Configuration - DEMO/TEST SETUP
# 这是一个示例配置文件，请根据实际情况修改

# 认证凭据 (请替换为真实值)
GPTPLUS_EMAIL=<EMAIL>
GPTPLUS_PASSWORD=demo_password_123

# 购买设置
DEFAULT_QUANTITY=1
MAX_QUANTITY=3
PREFERRED_PRODUCT=GPT-PLUS

# 安全设置 (重要: 测试时保持干运行模式开启)
DRY_RUN=true
MAX_DAILY_PURCHASES=5
ENABLE_PURCHASE_CONFIRMATION=true

# 机器人行为
REQUEST_DELAY=2.0
MAX_RETRIES=3
TIMEOUT=30

# 日志设置
LOG_LEVEL=INFO
LOG_FILE=bot.log
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(sample_config)
        print("✅ 示例配置文件已创建: .env")
        print("💡 请编辑 .env 文件，填入正确的登录凭据")
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
GPT Plus 购买机器人 Web 界面启动器

使用方法:
    python start_web_ui.py          # 启动Web界面
    python start_web_ui.py --help   # 显示此帮助信息

功能特性:
    🌐 现代化Web界面
    📊 实时状态监控
    ⚙️  可视化配置管理
    📈 购买历史查看
    📋 实时日志查看
    🔒 安全认证保护

访问信息:
    地址: http://localhost:5000
    默认用户名: admin
    默认密码: admin123

安全提醒:
    ⚠️  首次使用请保持测试模式开启
    ⚠️  确认配置正确后再关闭测试模式
    ⚠️  生产环境请修改默认登录密码
    ⚠️  妥善保管您的账户凭据

技术支持:
    如遇问题，请检查:
    1. Python 版本 (推荐 3.8+)
    2. 网络连接
    3. 防火墙设置
    4. 配置文件格式
"""
    print(help_text)

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        return
    
    print("🤖 GPT Plus 购买机器人 Web 界面启动器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        print(f"   当前版本: {sys.version}")
        return
    
    # 检查工作目录
    required_files = ['web_app.py', 'main.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("   请确保在正确的项目目录中运行此脚本")
        return
    
    # 启动Web界面
    success = start_web_interface()
    
    if success:
        print("\n✅ 启动完成")
    else:
        print("\n❌ 启动失败")
        print("\n💡 故障排除建议:")
        print("   1. 检查Python版本和依赖")
        print("   2. 确认端口5000未被占用")
        print("   3. 检查防火墙设置")
        print("   4. 查看错误日志")

if __name__ == '__main__':
    main()
