# Usage Examples

This document provides detailed examples of how to use the GPT Plus Purchase Bot in various scenarios.

## Table of Contents

1. [First Time Setup](#first-time-setup)
2. [Basic Operations](#basic-operations)
3. [Advanced Usage](#advanced-usage)
4. [Monitoring and Maintenance](#monitoring-and-maintenance)
5. [Troubleshooting Scenarios](#troubleshooting-scenarios)

## First Time Setup

### Complete Setup Process

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Run interactive setup
python main.py setup

# Follow the prompts:
# Email address: <EMAIL>
# Password: [hidden input]
# Default quantity [1]: 1
# Maximum quantity per purchase [5]: 3
# Preferred product type [GPT-PLUS]: GPT-PLUS
# Maximum daily purchases [10]: 5

# 3. Verify configuration
python main.py status

# 4. Test the bot
python main.py test
```

### Manual Configuration

If you prefer to create the `.env` file manually:

```bash
# Create .env file
cat > .env << EOF
GPTPLUS_EMAIL=<EMAIL>
GPTPLUS_PASSWORD=your_secure_password
DEFAULT_QUANTITY=1
MAX_QUANTITY=3
PREFERRED_PRODUCT=GPT-PLUS
DRY_RUN=true
REQUEST_DELAY=2.0
MAX_RETRIES=3
TIMEOUT=30
LOG_LEVEL=INFO
LOG_FILE=bot.log
ENABLE_PURCHASE_CONFIRMATION=true
MAX_DAILY_PURCHASES=5
EOF

# Test configuration
python main.py status
```

## Basic Operations

### Dry Run Testing

Always start with dry run mode to test functionality:

```bash
# Test basic functionality
python main.py test

# Run a single purchase test
python main.py run --product "GPT-PLUS" --quantity 1

# Interactive testing
python main.py run --interactive
```

### Making Real Purchases

⚠️ **CAUTION**: These commands make real purchases with real money!

```bash
# Single purchase with confirmation
python main.py run --product "GPT-PLUS" --quantity 1 --no-dry-run

# Multiple purchases
python main.py run --product "GPT-PLUS" --quantity 2 --no-dry-run

# Auto-select best available product
python main.py run --quantity 1 --no-dry-run
```

### Interactive Mode

For easier control, use interactive mode:

```bash
python main.py run --interactive

# This will show a menu:
# 1. Purchase product
# 2. View available products  
# 3. Check status
# 4. View purchase history
# 5. Exit
```

## Advanced Usage

### Batch Operations

For multiple purchases with safety checks:

```bash
# Purchase multiple items with delays
for i in {1..3}; do
    python main.py run --product "GPT-PLUS" --quantity 1 --no-dry-run
    sleep 30  # Wait 30 seconds between purchases
done
```

### Custom Configuration

Override configuration for specific runs:

```bash
# Use custom log level
python main.py run --log-level DEBUG

# Different quantities
python main.py run --quantity 3

# Specific product targeting
python main.py run --product "GPT-GO" --quantity 1
```

### Monitoring During Operation

Monitor the bot while it's running:

```bash
# In one terminal, run the bot
python main.py run --interactive

# In another terminal, monitor logs
tail -f bot.log

# Check status periodically
watch -n 30 'python main.py status'
```

## Monitoring and Maintenance

### Daily Monitoring

```bash
# Morning routine - check status
python main.py status

# Check yesterday's activity
python main.py history --days 1

# Verify configuration is still valid
python main.py test
```

### Weekly Maintenance

```bash
# Review weekly history
python main.py history --days 7

# Check for any configuration issues
python main.py status

# Clean up old log files (optional)
find logs/ -name "*.json" -mtime +30 -delete
```

### Performance Analysis

```bash
# Check current performance
python main.py status | grep -A 10 "Performance"

# Monitor request patterns
grep "Request recorded" bot.log | tail -20

# Check error patterns
grep "ERROR" bot.log | tail -10
```

## Troubleshooting Scenarios

### Authentication Issues

```bash
# Test authentication
python main.py test

# If authentication fails, re-setup credentials
python main.py setup

# Check for account issues
grep "authentication" bot.log | tail -5
```

### Network Problems

```bash
# Test with increased timeouts
TIMEOUT=60 python main.py test

# Check network connectivity
curl -I https://gptpluscz.com/

# Review network errors
grep "Network error" bot.log | tail -10
```

### Purchase Failures

```bash
# Check product availability
python main.py run --interactive
# Select option 2 to view products

# Review recent failures
python main.py history --days 1 | grep FAILED

# Check safety limits
python main.py status | grep -A 5 "Safety Status"
```

### Rate Limiting Issues

```bash
# Increase request delays
echo "REQUEST_DELAY=5.0" >> .env

# Check rate limit status
python main.py status | grep -A 10 "Rate Limit"

# Review rate limit errors
grep "rate limit" bot.log | tail -5
```

### Configuration Problems

```bash
# Validate current configuration
python main.py status

# Reset configuration
mv .env .env.backup
python main.py setup

# Compare configurations
diff .env.backup .env
```

## Safety Best Practices

### Before Each Session

```bash
# 1. Check daily limits
python main.py status | grep "Daily purchases"

# 2. Verify dry-run mode (for testing)
grep "DRY_RUN=true" .env

# 3. Test connectivity
python main.py test
```

### During Operation

```bash
# Monitor in real-time
tail -f bot.log | grep -E "(Purchase|Error|Warning)"

# Check status periodically
watch -n 60 'python main.py status'
```

### After Operation

```bash
# Review session results
python main.py history --days 1

# Check for any errors
grep "ERROR" bot.log | tail -10

# Verify purchase records
ls -la logs/monitoring/
```

## Emergency Procedures

### Stop All Operations

```bash
# If bot is running, press Ctrl+C to stop gracefully

# Check for any running processes
ps aux | grep python | grep main.py

# Force kill if necessary (last resort)
pkill -f "python main.py"
```

### Disable Real Purchases

```bash
# Immediately enable dry-run mode
sed -i 's/DRY_RUN=false/DRY_RUN=true/' .env

# Verify change
grep DRY_RUN .env
```

### Account Security

```bash
# Change password immediately if compromised
python main.py setup

# Clear stored credentials
python -c "
import keyring
keyring.delete_password('gptplus_bot', '<EMAIL>')
"

# Review recent activity
python main.py history --days 7
```

## Integration Examples

### Cron Job Setup

```bash
# Add to crontab for daily automated purchases
# (Only if you're absolutely sure!)
crontab -e

# Add line (example - runs at 9 AM daily):
# 0 9 * * * cd /path/to/GPTQG && python main.py run --quantity 1 --no-dry-run >> cron.log 2>&1
```

### Notification Integration

```bash
# Send email notification after purchase
python main.py run --quantity 1 --no-dry-run && \
echo "Purchase completed successfully" | mail -s "Bot Purchase Update" <EMAIL>
```

### Backup Integration

```bash
# Backup purchase records daily
tar -czf "backup_$(date +%Y%m%d).tar.gz" logs/ .env
```

Remember: Always test thoroughly in dry-run mode before making real purchases!
