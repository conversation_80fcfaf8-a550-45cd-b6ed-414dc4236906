{% extends "base.html" %}

{% block title %}日志 - GPT Plus 购买机器人{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-alt me-2"></i>系统日志
        </h1>
        <p class="text-muted">实时查看机器人运行日志和错误信息</p>
    </div>
</div>

<!-- Log Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="log-level-filter" class="form-label">日志级别</label>
                        <select class="form-select" id="log-level-filter" onchange="filterLogs()">
                            <option value="">全部</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARNING">WARNING</option>
                            <option value="ERROR">ERROR</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="log-search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="log-search" placeholder="搜索日志内容..." onkeyup="searchLogs()">
                    </div>
                    <div class="col-md-3">
                        <label for="auto-refresh" class="form-label">自动刷新</label>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" id="auto-refresh" checked onchange="toggleAutoRefresh()">
                            <label class="form-check-label" for="auto-refresh">启用</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-primary" onclick="downloadLogs()">
                                <i class="fas fa-download me-1"></i>下载日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="metric-value text-info" id="debug-count">0</div>
                        <div class="metric-label">DEBUG</div>
                    </div>
                    <div class="col-3">
                        <div class="metric-value text-success" id="info-count">0</div>
                        <div class="metric-label">INFO</div>
                    </div>
                    <div class="col-3">
                        <div class="metric-value text-warning" id="warning-count">0</div>
                        <div class="metric-label">WARNING</div>
                    </div>
                    <div class="col-3">
                        <div class="metric-value text-danger" id="error-count">0</div>
                        <div class="metric-label">ERROR</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Log Display -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-terminal me-2"></i>实时日志</span>
                <div>
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearLogDisplay()">
                        <i class="fas fa-trash me-1"></i>清空显示
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshLogs()">
                        <i class="fas fa-sync me-1"></i>刷新
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container" id="log-container">
                    {% if logs %}
                        {% for log_line in logs %}
                            <div class="log-line" data-original="{{ log_line }}">{{ log_line }}</div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <p>暂无日志内容</p>
                            <p class="small">日志将在机器人运行时显示</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Log Entry Detail Modal -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>日志详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="log-container" id="log-detail-content" style="max-height: 400px;">
                    <!-- Log detail will be shown here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .log-line {
        padding: 4px 8px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        line-height: 1.4;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .log-line:hover {
        background-color: rgba(255,255,255,0.1);
    }
    
    .log-line.debug { color: #64748b; }
    .log-line.info { color: #10b981; }
    .log-line.warning { color: #f59e0b; }
    .log-line.error { color: #ef4444; }
    .log-line.filtered { display: none; }
    
    .log-highlight {
        background-color: rgba(255, 255, 0, 0.3);
        padding: 1px 2px;
        border-radius: 2px;
    }
    
    .log-container {
        max-height: 600px;
        overflow-y: auto;
        font-size: 0.9rem;
    }
    
    .log-container::-webkit-scrollbar {
        width: 8px;
    }
    
    .log-container::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
        border-radius: 4px;
    }
    
    .log-container::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 4px;
    }
    
    .log-container::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let autoRefreshInterval;
    let isAutoRefreshEnabled = true;

    // Initialize log processing
    document.addEventListener('DOMContentLoaded', function() {
        processLogLines();
        updateLogCounts();
        startAutoRefresh();
    });

    function processLogLines() {
        const logLines = document.querySelectorAll('.log-line');
        logLines.forEach(line => {
            const text = line.textContent;
            
            // Detect log level and apply styling
            if (text.includes('DEBUG')) {
                line.classList.add('debug');
            } else if (text.includes('INFO')) {
                line.classList.add('info');
            } else if (text.includes('WARNING')) {
                line.classList.add('warning');
            } else if (text.includes('ERROR')) {
                line.classList.add('error');
            }
            
            // Add click handler for detail view
            line.addEventListener('click', function() {
                showLogDetail(text);
            });
        });
    }

    function updateLogCounts() {
        const debugCount = document.querySelectorAll('.log-line.debug').length;
        const infoCount = document.querySelectorAll('.log-line.info').length;
        const warningCount = document.querySelectorAll('.log-line.warning').length;
        const errorCount = document.querySelectorAll('.log-line.error').length;
        
        document.getElementById('debug-count').textContent = debugCount;
        document.getElementById('info-count').textContent = infoCount;
        document.getElementById('warning-count').textContent = warningCount;
        document.getElementById('error-count').textContent = errorCount;
    }

    function filterLogs() {
        const selectedLevel = document.getElementById('log-level-filter').value;
        const logLines = document.querySelectorAll('.log-line');
        
        logLines.forEach(line => {
            if (!selectedLevel) {
                line.classList.remove('filtered');
            } else {
                if (line.classList.contains(selectedLevel.toLowerCase())) {
                    line.classList.remove('filtered');
                } else {
                    line.classList.add('filtered');
                }
            }
        });
    }

    function searchLogs() {
        const searchTerm = document.getElementById('log-search').value.toLowerCase();
        const logLines = document.querySelectorAll('.log-line');
        
        logLines.forEach(line => {
            const originalText = line.getAttribute('data-original') || line.textContent;
            
            if (!searchTerm) {
                // Reset to original text
                line.innerHTML = originalText;
                line.style.display = '';
            } else {
                if (originalText.toLowerCase().includes(searchTerm)) {
                    // Highlight search term
                    const highlightedText = originalText.replace(
                        new RegExp(searchTerm, 'gi'),
                        match => `<span class="log-highlight">${match}</span>`
                    );
                    line.innerHTML = highlightedText;
                    line.style.display = '';
                } else {
                    line.style.display = 'none';
                }
            }
        });
    }

    function refreshLogs() {
        fetch('/api/logs')
            .then(response => response.json())
            .then(data => {
                if (data.logs) {
                    const container = document.getElementById('log-container');
                    container.innerHTML = '';
                    
                    data.logs.forEach(logLine => {
                        const div = document.createElement('div');
                        div.className = 'log-line';
                        div.setAttribute('data-original', logLine);
                        div.textContent = logLine;
                        container.appendChild(div);
                    });
                    
                    processLogLines();
                    updateLogCounts();
                    
                    // Apply current filters
                    filterLogs();
                    searchLogs();
                    
                    // Scroll to bottom
                    container.scrollTop = container.scrollHeight;
                }
            })
            .catch(error => {
                console.error('Error refreshing logs:', error);
            });
    }

    function clearLogDisplay() {
        if (confirm('确定要清空日志显示吗？这不会删除日志文件。')) {
            document.getElementById('log-container').innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-broom fa-2x mb-2"></i>
                    <p>显示已清空</p>
                    <p class="small">点击刷新按钮重新加载日志</p>
                </div>
            `;
            updateLogCounts();
        }
    }

    function downloadLogs() {
        const link = document.createElement('a');
        link.href = '/api/download_logs';
        link.download = `bot_logs_${new Date().toISOString().split('T')[0]}.log`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showAlert('success', '日志文件下载已开始');
    }

    function showLogDetail(logText) {
        document.getElementById('log-detail-content').textContent = logText;
        const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
        modal.show();
    }

    function toggleAutoRefresh() {
        isAutoRefreshEnabled = document.getElementById('auto-refresh').checked;
        
        if (isAutoRefreshEnabled) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    }

    function startAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
        
        if (isAutoRefreshEnabled) {
            autoRefreshInterval = setInterval(refreshLogs, 5000); // Refresh every 5 seconds
        }
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid.py-4');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Socket.IO integration for real-time logs
    if (typeof socket !== 'undefined') {
        socket.on('new_log', function(data) {
            const container = document.getElementById('log-container');
            const div = document.createElement('div');
            div.className = 'log-line';
            div.setAttribute('data-original', data.message);
            div.textContent = data.message;
            
            // Apply styling based on log level
            if (data.level) {
                div.classList.add(data.level.toLowerCase());
            }
            
            // Add click handler
            div.addEventListener('click', function() {
                showLogDetail(data.message);
            });
            
            container.appendChild(div);
            
            // Keep only last 1000 lines
            const lines = container.querySelectorAll('.log-line');
            if (lines.length > 1000) {
                lines[0].remove();
            }
            
            // Auto-scroll to bottom
            container.scrollTop = container.scrollHeight;
            
            updateLogCounts();
        });
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
    });
</script>
{% endblock %}
