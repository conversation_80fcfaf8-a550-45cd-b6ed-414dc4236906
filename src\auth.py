"""
Authentication module for the GPT Plus purchase bot.
Handles login, session management, and authentication state tracking.
"""

import time
import logging
import requests
from typing import Optional, Dict, Any
from urllib.parse import urljoin
from bs4 import BeautifulSoup
from fake_useragent import UserAgent

from .config import BotConfig


class AuthenticationError(Exception):
    """Raised when authentication fails."""
    pass


class SessionManager:
    """Manages HTTP sessions and authentication state."""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = logging.getLogger('gptplus_bot.auth')
        self.session = requests.Session()
        self.is_authenticated = False
        self.user_info: Optional[Dict[str, Any]] = None
        
        # Set up session headers
        ua = UserAgent()
        self.session.headers.update({
            'User-Agent': config.user_agent or ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Configure session timeouts and retries
        self.session.timeout = config.timeout
    
    def login(self, email: str, password: str) -> bool:
        """
        Authenticate with the website using email and password.
        
        Args:
            email: User email address
            password: User password
            
        Returns:
            True if authentication successful, False otherwise
            
        Raises:
            AuthenticationError: If login fails due to invalid credentials or other errors
        """
        self.logger.info(f"Attempting to login with email: {email}")
        
        try:
            # First, get the login page to extract any CSRF tokens or form data
            login_url = urljoin(self.config.base_url, '/login')
            
            self.logger.debug(f"Fetching login page: {login_url}")
            response = self.session.get(login_url)
            response.raise_for_status()
            
            # Parse the login form
            soup = BeautifulSoup(response.text, 'html.parser')
            login_form = soup.find('form')
            
            if not login_form:
                raise AuthenticationError("Could not find login form on the page")
            
            # Extract form action and method
            form_action = login_form.get('action', '/login')
            form_method = login_form.get('method', 'POST').upper()
            
            # Build the login URL
            if form_action.startswith('/'):
                login_submit_url = urljoin(self.config.base_url, form_action)
            else:
                login_submit_url = form_action
            
            # Prepare login data
            login_data = {
                'email': email,
                'password': password
            }
            
            # Extract any hidden form fields (CSRF tokens, etc.)
            hidden_inputs = login_form.find_all('input', type='hidden')
            for hidden_input in hidden_inputs:
                name = hidden_input.get('name')
                value = hidden_input.get('value', '')
                if name:
                    login_data[name] = value
                    self.logger.debug(f"Added hidden field: {name}")
            
            # Add delay to respect rate limiting
            time.sleep(self.config.request_delay)
            
            # Submit login form
            self.logger.debug(f"Submitting login to: {login_submit_url}")
            if form_method == 'GET':
                response = self.session.get(login_submit_url, params=login_data)
            else:
                response = self.session.post(login_submit_url, data=login_data)
            
            response.raise_for_status()
            
            # Check if login was successful
            if self._verify_authentication(response):
                self.is_authenticated = True
                self.logger.info("Login successful")
                return True
            else:
                self.logger.warning("Login failed - invalid credentials or other error")
                raise AuthenticationError("Invalid credentials or login failed")
                
        except requests.RequestException as e:
            self.logger.error(f"Network error during login: {e}")
            raise AuthenticationError(f"Network error: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error during login: {e}")
            raise AuthenticationError(f"Login error: {e}")
    
    def _verify_authentication(self, response: requests.Response) -> bool:
        """
        Verify if authentication was successful by checking the response.
        
        Args:
            response: The response from the login request
            
        Returns:
            True if authenticated, False otherwise
        """
        # Check for common indicators of successful login
        
        # 1. Check for redirect to dashboard or home page
        if response.history and any('dashboard' in r.url or 'home' in r.url for r in response.history):
            return True
        
        # 2. Check if we're no longer on the login page
        if '/login' not in response.url and response.status_code == 200:
            return True
        
        # 3. Parse the response content for success indicators
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Look for logout links or user info (indicates logged in)
        logout_link = soup.find('a', href=lambda x: x and 'logout' in x.lower())
        if logout_link:
            return True
        
        # Look for user dashboard elements
        dashboard_elements = soup.find_all(['div', 'span'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['dashboard', 'profile', 'account', 'user']
        ))
        if dashboard_elements:
            return True
        
        # 4. Check for error messages (indicates login failed)
        error_indicators = [
            'invalid', 'incorrect', 'wrong', 'failed', 'error',
            '错误', '失败', '无效'  # Chinese error terms
        ]
        
        page_text = response.text.lower()
        if any(indicator in page_text for indicator in error_indicators):
            return False
        
        # 5. Check for login form still present (indicates login failed)
        login_form = soup.find('form')
        if login_form:
            # Check if it's still a login form
            email_input = login_form.find('input', {'name': 'email'}) or login_form.find('input', {'type': 'email'})
            password_input = login_form.find('input', {'name': 'password'}) or login_form.find('input', {'type': 'password'})
            
            if email_input and password_input:
                return False
        
        # If we can't determine definitively, assume success if status is OK
        return response.status_code == 200
    
    def logout(self) -> bool:
        """
        Log out from the website.
        
        Returns:
            True if logout successful, False otherwise
        """
        if not self.is_authenticated:
            self.logger.info("Already logged out")
            return True
        
        try:
            # Try to find and use logout URL
            logout_url = urljoin(self.config.base_url, '/logout')
            
            self.logger.info("Attempting to logout")
            response = self.session.get(logout_url)
            
            # Clear authentication state
            self.is_authenticated = False
            self.user_info = None
            
            self.logger.info("Logout completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during logout: {e}")
            # Clear state anyway
            self.is_authenticated = False
            self.user_info = None
            return False
    
    def check_authentication(self) -> bool:
        """
        Check if the current session is still authenticated.
        
        Returns:
            True if still authenticated, False otherwise
        """
        if not self.is_authenticated:
            return False
        
        try:
            # Try to access a protected page to verify session
            dashboard_url = urljoin(self.config.base_url, '/')
            response = self.session.get(dashboard_url)
            
            if response.status_code == 200:
                # Check if we're redirected to login page
                if '/login' in response.url:
                    self.is_authenticated = False
                    return False
                
                return True
            else:
                self.is_authenticated = False
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking authentication: {e}")
            self.is_authenticated = False
            return False
    
    def get_session(self) -> requests.Session:
        """
        Get the current authenticated session.
        
        Returns:
            The requests session object
            
        Raises:
            AuthenticationError: If not authenticated
        """
        if not self.is_authenticated:
            raise AuthenticationError("Not authenticated. Please login first.")
        
        return self.session
    
    def close(self):
        """Close the session and clean up resources."""
        if self.session:
            self.session.close()
        self.is_authenticated = False
        self.user_info = None
