#!/usr/bin/env python3
"""
简单测试信号处理修复
"""

import sys
import os
import threading

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_in_thread():
    """在线程中测试机器人创建"""
    try:
        from src.bot import GPTPlusPurchaseBot
        from src.config import get_config
        
        config = get_config()
        bot = GPTPlusPurchaseBot(config)
        print("✅ 机器人在线程中创建成功!")
        bot.shutdown()
        return True
    except ValueError as e:
        if "signal only works in main thread" in str(e):
            print("❌ 信号处理错误仍然存在")
            return False
        else:
            print(f"⚠️  其他错误: {e}")
            return True
    except Exception as e:
        print(f"⚠️  其他错误: {e}")
        return True

def main():
    print("🧪 测试信号处理修复...")
    
    # 在子线程中测试
    result = [False]
    
    def thread_test():
        result[0] = test_in_thread()
    
    thread = threading.Thread(target=thread_test)
    thread.start()
    thread.join()
    
    if result[0]:
        print("🎉 测试通过! 信号处理问题已修复")
    else:
        print("❌ 测试失败")
    
    return result[0]

if __name__ == '__main__':
    main()
