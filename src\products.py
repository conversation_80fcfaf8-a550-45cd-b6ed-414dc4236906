"""
Product discovery and selection module for the GPT Plus purchase bot.
Handles browsing products, checking availability, and selecting items for purchase.
"""

import time
import logging
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin
from bs4 import BeautifulSoup
from dataclasses import dataclass

from .config import BotConfig
from .auth import SessionManager, AuthenticationError


@dataclass
class Product:
    """Represents a product available for purchase."""
    id: str
    name: str
    price: float
    currency: str
    stock: int
    delivery_method: str
    description: str = ""
    url: str = ""
    is_available: bool = True
    
    def __str__(self):
        return f"{self.name} - {self.currency}{self.price} (Stock: {self.stock})"


class ProductDiscoveryError(Exception):
    """Raised when product discovery fails."""
    pass


class ProductManager:
    """Manages product discovery, selection, and availability checking."""
    
    def __init__(self, config: BotConfig, session_manager: SessionManager):
        self.config = config
        self.session_manager = session_manager
        self.logger = logging.getLogger('gptplus_bot.products')
        self._products_cache: List[Product] = []
        self._cache_timestamp: float = 0
        self._cache_duration = 300  # 5 minutes
    
    def discover_products(self, force_refresh: bool = False) -> List[Product]:
        """
        Discover all available products on the website.
        
        Args:
            force_refresh: Force refresh of product cache
            
        Returns:
            List of available products
            
        Raises:
            ProductDiscoveryError: If product discovery fails
            AuthenticationError: If not authenticated
        """
        # Check cache first
        current_time = time.time()
        if (not force_refresh and 
            self._products_cache and 
            current_time - self._cache_timestamp < self._cache_duration):
            self.logger.debug("Returning cached products")
            return self._products_cache
        
        self.logger.info("Discovering products...")
        
        try:
            session = self.session_manager.get_session()
            
            # Get the main products page
            products_url = urljoin(self.config.base_url, '/')
            response = session.get(products_url)
            response.raise_for_status()
            
            # Add delay to respect rate limiting
            time.sleep(self.config.request_delay)
            
            # Parse the products page
            soup = BeautifulSoup(response.text, 'html.parser')
            products = self._parse_products(soup)
            
            # Update cache
            self._products_cache = products
            self._cache_timestamp = current_time
            
            self.logger.info(f"Discovered {len(products)} products")
            return products
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.error(f"Error discovering products: {e}")
            raise ProductDiscoveryError(f"Failed to discover products: {e}")
    
    def _parse_products(self, soup: BeautifulSoup) -> List[Product]:
        """
        Parse products from the HTML page.
        
        Args:
            soup: BeautifulSoup object of the products page
            
        Returns:
            List of parsed products
        """
        products = []
        
        # Look for product rows/cards
        product_elements = soup.find_all('tr', class_='products-row')
        
        if not product_elements:
            # Try alternative selectors
            product_elements = soup.find_all(['div', 'tr'], class_=lambda x: x and 'product' in x.lower())
        
        for element in product_elements:
            try:
                product = self._parse_single_product(element)
                if product:
                    products.append(product)
            except Exception as e:
                self.logger.warning(f"Error parsing product element: {e}")
                continue
        
        return products
    
    def _parse_single_product(self, element) -> Optional[Product]:
        """
        Parse a single product from an HTML element.
        
        Args:
            element: BeautifulSoup element containing product info
            
        Returns:
            Product object or None if parsing fails
        """
        try:
            # Extract product name
            name_element = element.find(['h3', 'h4', 'span', 'div'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['name', 'title', 'product']
            ))
            
            if not name_element:
                # Try to find by text content
                name_candidates = element.find_all(text=re.compile(r'GPT|PLUS|GO', re.I))
                if name_candidates:
                    name = name_candidates[0].strip()
                else:
                    return None
            else:
                name = name_element.get_text(strip=True)
            
            if not name:
                return None
            
            # Extract price - look for price patterns in the element
            price_text = element.get_text()
            price_match = re.search(r'[¥$€£]\s*(\d+(?:\.\d+)?)', price_text)
            if price_match:
                currency_match = re.search(r'([¥$€£])', price_text)
                currency = currency_match.group(1) if currency_match else "¥"
                price = float(price_match.group(1))
            else:
                # Try alternative price extraction
                price_elements = element.find_all(text=re.compile(r'\d+\.\d+|\d+'))
                if price_elements:
                    for price_elem in price_elements:
                        if re.search(r'\d+\.\d+', price_elem):
                            price = float(re.search(r'(\d+\.\d+)', price_elem).group(1))
                            currency = "¥"
                            break
                    else:
                        price = 0.0
                        currency = "¥"
                else:
                    price = 0.0
                    currency = "¥"
            
            # Extract stock information
            stock_element = element.find(text=re.compile(r'库存|stock', re.I))
            if stock_element:
                stock_text = stock_element.strip()
                stock_match = re.search(r'(\d+)', stock_text)
                stock = int(stock_match.group(1)) if stock_match else 0
            else:
                stock = 0
            
            # Extract delivery method
            delivery_element = element.find(text=re.compile(r'自动发货|automatic|manual', re.I))
            if delivery_element:
                delivery_method = delivery_element.strip()
            else:
                delivery_method = "自动发货"  # Default to automatic
            
            # Generate product ID from name
            product_id = re.sub(r'[^a-zA-Z0-9]', '_', name.lower())
            
            # Determine availability
            is_available = stock > 0
            
            product = Product(
                id=product_id,
                name=name,
                price=price,
                currency=currency,
                stock=stock,
                delivery_method=delivery_method,
                is_available=is_available
            )
            
            self.logger.debug(f"Parsed product: {product}")
            return product
            
        except Exception as e:
            self.logger.warning(f"Error parsing product element: {e}")
            return None
    
    def find_product_by_name(self, name: str, partial_match: bool = True) -> Optional[Product]:
        """
        Find a product by name.
        
        Args:
            name: Product name to search for
            partial_match: Allow partial name matching
            
        Returns:
            Product object or None if not found
        """
        products = self.discover_products()
        
        for product in products:
            if partial_match:
                if name.lower() in product.name.lower():
                    return product
            else:
                if name.lower() == product.name.lower():
                    return product
        
        return None
    
    def find_products_by_type(self, product_type: str) -> List[Product]:
        """
        Find products by type (e.g., 'GPT-PLUS', 'GPT-GO').
        
        Args:
            product_type: Type of product to search for
            
        Returns:
            List of matching products
        """
        products = self.discover_products()
        matching_products = []
        
        for product in products:
            if product_type.lower() in product.name.lower():
                matching_products.append(product)
        
        return matching_products
    
    def get_available_products(self) -> List[Product]:
        """
        Get all products that are currently available (in stock).
        
        Returns:
            List of available products
        """
        products = self.discover_products()
        return [p for p in products if p.is_available and p.stock > 0]
    
    def check_product_availability(self, product: Product) -> bool:
        """
        Check if a specific product is still available.
        
        Args:
            product: Product to check
            
        Returns:
            True if available, False otherwise
        """
        # Force refresh to get latest stock info
        current_products = self.discover_products(force_refresh=True)
        
        for current_product in current_products:
            if current_product.id == product.id:
                return current_product.is_available and current_product.stock > 0
        
        return False
    
    def select_best_product(self, preferred_type: str = None) -> Optional[Product]:
        """
        Select the best available product based on preferences.
        
        Args:
            preferred_type: Preferred product type
            
        Returns:
            Best available product or None
        """
        available_products = self.get_available_products()
        
        if not available_products:
            self.logger.warning("No products available")
            return None
        
        # If preferred type specified, try to find it first
        if preferred_type:
            preferred_products = [p for p in available_products 
                                if preferred_type.lower() in p.name.lower()]
            if preferred_products:
                # Sort by stock (highest first) and return the best one
                preferred_products.sort(key=lambda x: x.stock, reverse=True)
                return preferred_products[0]
        
        # Otherwise, return the product with highest stock
        available_products.sort(key=lambda x: x.stock, reverse=True)
        return available_products[0]
    
    def get_product_details(self, product: Product) -> Dict[str, Any]:
        """
        Get detailed information about a product.
        
        Args:
            product: Product to get details for
            
        Returns:
            Dictionary with detailed product information
        """
        return {
            'id': product.id,
            'name': product.name,
            'price': product.price,
            'currency': product.currency,
            'stock': product.stock,
            'delivery_method': product.delivery_method,
            'description': product.description,
            'is_available': product.is_available,
            'url': product.url
        }
