# GPT Plus 购买机器人 Web 界面使用指南

## 🌐 界面概览

我已经为您创建了一个现代化的Web界面，让您可以通过浏览器轻松控制和监控GPT Plus购买机器人。

### ✨ 主要功能

- **🎛️ 仪表板**: 实时监控机器人状态和购买活动
- **⚙️ 配置管理**: 可视化配置机器人参数
- **📊 购买历史**: 查看和管理所有购买记录
- **📋 实时日志**: 监控机器人运行日志
- **🔒 安全认证**: 登录保护，防止未授权访问
- **📱 响应式设计**: 支持桌面和移动设备

## 🚀 快速启动

### 方法一：使用启动脚本 (推荐)
```bash
# Windows用户
双击 start_web_ui.bat

# 或者使用Python脚本
python start_web_ui.py
```

### 方法二：直接启动
```bash
# 安装Web依赖
pip install flask flask-socketio flask-login eventlet

# 启动Web界面
python web_app.py
```

### 访问界面
- **地址**: http://localhost:5000
- **默认用户名**: admin
- **默认密码**: admin123

## 📱 界面功能详解

### 1. 🎛️ 仪表板页面

**实时监控指标**
- 今日购买次数
- 最近购买记录
- 成功率统计
- 每日限额显示

**机器人控制面板**
- 启动/停止机器人
- 查看运行状态
- 显示当前模式 (测试/实际)
- 显示登录账户

**快速购买功能**
- 选择产品类型
- 设置购买数量
- 一键执行购买

**配置概览**
- 当前配置摘要
- 运行模式显示
- 快速配置链接

### 2. ⚙️ 配置页面

**认证设置**
- 邮箱地址配置
- 密码安全更新
- 凭据验证

**购买设置**
- 首选产品选择
- 默认购买数量
- 最大购买限制

**安全设置**
- 测试模式开关 (强烈推荐保持开启)
- 购买确认提示
- 每日购买限额

**性能设置**
- 请求延迟时间
- 最大重试次数
- 超时时间设置

**日志设置**
- 日志级别选择
- 日志文件路径

### 3. 📊 购买历史页面

**筛选功能**
- 时间范围选择 (今天/7天/30天/90天/一年)
- 状态筛选 (成功/失败/处理中)
- 产品类型筛选

**详细记录**
- 购买时间和产品信息
- 数量和金额详情
- 成功/失败状态
- 测试/实际模式标识

**操作功能**
- 查看详细信息
- 重试失败的购买
- 导出历史记录

### 4. 📋 日志页面

**实时日志显示**
- 自动刷新 (每5秒)
- 日志级别筛选
- 关键词搜索
- 彩色分级显示

**日志管理**
- 下载日志文件
- 清空显示内容
- 手动刷新

## 🔒 安全功能

### 登录认证
- 用户名/密码保护
- 会话管理
- 自动登出

### 默认凭据
- **用户名**: admin
- **密码**: admin123
- ⚠️ **重要**: 生产环境请修改默认密码

### 安全建议
1. 首次使用保持测试模式开启
2. 确认配置正确后再关闭测试模式
3. 设置合理的购买限额
4. 定期检查购买历史
5. 妥善保管登录凭据

## 🎨 界面特色

### 现代化设计
- 响应式布局，支持各种屏幕尺寸
- 渐变色彩和阴影效果
- 流畅的动画过渡
- 直观的图标和状态指示

### 实时更新
- WebSocket连接实现实时状态更新
- 自动刷新购买状态
- 实时日志流显示
- 即时错误提醒

### 用户体验
- 一键操作，简化流程
- 智能表单验证
- 友好的错误提示
- 快捷键支持

## 🛠️ 技术架构

### 前端技术
- **Bootstrap 5**: 响应式UI框架
- **Font Awesome**: 图标库
- **Chart.js**: 数据可视化
- **Socket.IO**: 实时通信

### 后端技术
- **Flask**: Web应用框架
- **Flask-SocketIO**: WebSocket支持
- **Flask-Login**: 用户认证
- **Eventlet**: 异步处理

### 集成特性
- 与现有机器人代码完全集成
- 保持所有安全特性
- 支持所有配置选项
- 完整的错误处理

## 🔧 故障排除

### 常见问题

**1. 无法访问Web界面**
- 检查端口5000是否被占用
- 确认防火墙设置
- 验证Python版本 (需要3.7+)

**2. 登录失败**
- 确认用户名: admin
- 确认密码: admin123
- 清除浏览器缓存

**3. 机器人启动失败**
- 检查配置文件 (.env)
- 验证登录凭据
- 查看错误日志

**4. 购买功能异常**
- 确认机器人正在运行
- 检查网络连接
- 验证账户状态

### 日志查看
- Web界面: 访问日志页面
- 文件: 查看 bot.log 文件
- 控制台: 启动时的输出信息

## 📞 技术支持

### 获取帮助
1. 查看Web界面的日志页面
2. 检查控制台输出信息
3. 查看项目文档和README
4. 确认配置文件格式正确

### 最佳实践
1. 始终在测试模式下验证配置
2. 定期备份配置和日志
3. 监控购买历史和成功率
4. 保持软件更新

## 🎉 享受使用

现在您可以通过现代化的Web界面轻松管理GPT Plus购买机器人了！

- 🌐 **访问**: http://localhost:5000
- 🔑 **登录**: admin / admin123
- 🛡️ **安全**: 测试模式默认开启
- 📊 **监控**: 实时状态和历史记录
- ⚙️ **配置**: 可视化参数管理

祝您使用愉快！ 🚀
