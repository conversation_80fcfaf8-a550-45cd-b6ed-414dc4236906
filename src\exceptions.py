"""
Custom exceptions for the GPT Plus purchase bot.
Provides specific error types for better error handling and recovery.
"""


class BotError(Exception):
    """Base exception for all bot-related errors."""
    
    def __init__(self, message: str, error_code: str = None, recoverable: bool = True):
        super().__init__(message)
        self.error_code = error_code
        self.recoverable = recoverable


class ConfigurationError(BotError):
    """Raised when there are configuration issues."""
    
    def __init__(self, message: str):
        super().__init__(message, "CONFIG_ERROR", recoverable=False)


class AuthenticationError(BotError):
    """Raised when authentication fails."""
    
    def __init__(self, message: str, recoverable: bool = True):
        super().__init__(message, "AUTH_ERROR", recoverable=recoverable)


class NetworkError(BotError):
    """Raised when network operations fail."""
    
    def __init__(self, message: str, status_code: int = None):
        super().__init__(message, "NETWORK_ERROR", recoverable=True)
        self.status_code = status_code


class ProductError(BotError):
    """Raised when product-related operations fail."""
    
    def __init__(self, message: str, product_name: str = None):
        super().__init__(message, "PRODUCT_ERROR", recoverable=True)
        self.product_name = product_name


class OutOfStockError(ProductError):
    """Raised when a product is out of stock."""
    
    def __init__(self, product_name: str, requested_quantity: int = None):
        message = f"Product '{product_name}' is out of stock"
        if requested_quantity:
            message += f" (requested: {requested_quantity})"
        super().__init__(message, product_name)
        self.error_code = "OUT_OF_STOCK"
        self.recoverable = False


class PurchaseError(BotError):
    """Raised when purchase operations fail."""
    
    def __init__(self, message: str, order_id: str = None):
        super().__init__(message, "PURCHASE_ERROR", recoverable=True)
        self.order_id = order_id


class PaymentError(PurchaseError):
    """Raised when payment processing fails."""
    
    def __init__(self, message: str, transaction_id: str = None):
        super().__init__(message, transaction_id)
        self.error_code = "PAYMENT_ERROR"
        self.recoverable = False


class RateLimitError(BotError):
    """Raised when rate limits are exceeded."""
    
    def __init__(self, message: str, retry_after: int = None):
        super().__init__(message, "RATE_LIMIT_ERROR", recoverable=True)
        self.retry_after = retry_after


class SafetyError(BotError):
    """Raised when safety limits are exceeded."""
    
    def __init__(self, message: str, limit_type: str = None):
        super().__init__(message, "SAFETY_ERROR", recoverable=False)
        self.limit_type = limit_type


class ValidationError(BotError):
    """Raised when input validation fails."""
    
    def __init__(self, message: str, field_name: str = None):
        super().__init__(message, "VALIDATION_ERROR", recoverable=False)
        self.field_name = field_name


class SessionError(BotError):
    """Raised when session management fails."""
    
    def __init__(self, message: str):
        super().__init__(message, "SESSION_ERROR", recoverable=True)


class ParsingError(BotError):
    """Raised when HTML/data parsing fails."""
    
    def __init__(self, message: str, url: str = None):
        super().__init__(message, "PARSING_ERROR", recoverable=True)
        self.url = url


class TimeoutError(NetworkError):
    """Raised when operations timeout."""
    
    def __init__(self, message: str, timeout_duration: int = None):
        super().__init__(message)
        self.error_code = "TIMEOUT_ERROR"
        self.timeout_duration = timeout_duration


class MaintenanceError(BotError):
    """Raised when the website is under maintenance."""
    
    def __init__(self, message: str = "Website is under maintenance"):
        super().__init__(message, "MAINTENANCE_ERROR", recoverable=True)


class CaptchaError(BotError):
    """Raised when a CAPTCHA is encountered."""
    
    def __init__(self, message: str = "CAPTCHA verification required"):
        super().__init__(message, "CAPTCHA_ERROR", recoverable=False)


class InsufficientFundsError(PaymentError):
    """Raised when there are insufficient funds for purchase."""
    
    def __init__(self, message: str = "Insufficient funds for purchase"):
        super().__init__(message)
        self.error_code = "INSUFFICIENT_FUNDS"


class ProductNotFoundError(ProductError):
    """Raised when a requested product is not found."""
    
    def __init__(self, product_name: str):
        super().__init__(f"Product '{product_name}' not found", product_name)
        self.error_code = "PRODUCT_NOT_FOUND"
        self.recoverable = False


class InvalidQuantityError(ValidationError):
    """Raised when an invalid quantity is specified."""
    
    def __init__(self, quantity: int, max_allowed: int = None):
        message = f"Invalid quantity: {quantity}"
        if max_allowed:
            message += f" (max allowed: {max_allowed})"
        super().__init__(message, "quantity")
        self.quantity = quantity
        self.max_allowed = max_allowed


class DailyLimitExceededError(SafetyError):
    """Raised when daily purchase limits are exceeded."""
    
    def __init__(self, current_count: int, limit: int):
        message = f"Daily purchase limit exceeded: {current_count}/{limit}"
        super().__init__(message, "daily_limit")
        self.current_count = current_count
        self.limit = limit


class SessionLimitExceededError(SafetyError):
    """Raised when session limits are exceeded."""
    
    def __init__(self, current_count: int, limit: int, limit_type: str):
        message = f"Session {limit_type} limit exceeded: {current_count}/{limit}"
        super().__init__(message, f"session_{limit_type}")
        self.current_count = current_count
        self.limit = limit


def get_error_category(error: Exception) -> str:
    """
    Get the category of an error for handling purposes.
    
    Args:
        error: The exception to categorize
        
    Returns:
        Error category string
    """
    if isinstance(error, (AuthenticationError, SessionError)):
        return "authentication"
    elif isinstance(error, (NetworkError, TimeoutError)):
        return "network"
    elif isinstance(error, (ProductError, OutOfStockError, ProductNotFoundError)):
        return "product"
    elif isinstance(error, (PurchaseError, PaymentError, InsufficientFundsError)):
        return "purchase"
    elif isinstance(error, (SafetyError, DailyLimitExceededError, SessionLimitExceededError)):
        return "safety"
    elif isinstance(error, (ValidationError, InvalidQuantityError)):
        return "validation"
    elif isinstance(error, (RateLimitError, MaintenanceError)):
        return "temporary"
    elif isinstance(error, (CaptchaError, ConfigurationError)):
        return "manual_intervention"
    else:
        return "unknown"


def is_recoverable_error(error: Exception) -> bool:
    """
    Determine if an error is recoverable (can be retried).
    
    Args:
        error: The exception to check
        
    Returns:
        True if the error is recoverable, False otherwise
    """
    if isinstance(error, BotError):
        return error.recoverable
    
    # For non-BotError exceptions, make conservative assumptions
    if isinstance(error, (ConnectionError, TimeoutError)):
        return True
    elif isinstance(error, (ValueError, TypeError)):
        return False
    else:
        return True  # Default to recoverable for unknown errors


def get_retry_delay(error: Exception, attempt: int) -> float:
    """
    Get the recommended retry delay for an error.
    
    Args:
        error: The exception that occurred
        attempt: The current attempt number (0-based)
        
    Returns:
        Delay in seconds before retry
    """
    base_delays = {
        "network": 2.0,
        "authentication": 5.0,
        "product": 1.0,
        "purchase": 3.0,
        "temporary": 10.0,
        "unknown": 2.0
    }
    
    category = get_error_category(error)
    base_delay = base_delays.get(category, 2.0)
    
    # Exponential backoff with jitter
    import random
    delay = base_delay * (2 ** attempt)
    jitter = random.uniform(0.8, 1.2)
    
    return min(delay * jitter, 60.0)  # Cap at 60 seconds
