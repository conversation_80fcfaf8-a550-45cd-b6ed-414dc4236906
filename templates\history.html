{% extends "base.html" %}

{% block title %}购买历史 - GPT Plus 购买机器人{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">
            <i class="fas fa-history me-2"></i>购买历史
        </h1>
        <p class="text-muted">查看和管理所有购买记录</p>
    </div>
</div>

<!-- Filter Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="days" class="form-label">时间范围</label>
                        <select class="form-select" id="days" name="days" onchange="this.form.submit()">
                            <option value="1" {% if days == 1 %}selected{% endif %}>今天</option>
                            <option value="7" {% if days == 7 %}selected{% endif %}>最近7天</option>
                            <option value="30" {% if days == 30 %}selected{% endif %}>最近30天</option>
                            <option value="90" {% if days == 90 %}selected{% endif %}>最近90天</option>
                            <option value="365" {% if days == 365 %}selected{% endif %}>最近一年</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                            <option value="">全部</option>
                            <option value="success">成功</option>
                            <option value="failed">失败</option>
                            <option value="pending">处理中</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="product" class="form-label">产品</label>
                        <select class="form-select" id="product" name="product" onchange="this.form.submit()">
                            <option value="">全部产品</option>
                            <option value="GPT-PLUS">GPT-PLUS</option>
                            <option value="GPT-GO">GPT-GO</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-primary" onclick="exportHistory()">
                                <i class="fas fa-download me-1"></i>导出
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="metric-value text-success">{{ history|selectattr('success')|list|length }}</div>
                        <div class="metric-label">成功</div>
                    </div>
                    <div class="col-4">
                        <div class="metric-value text-danger">{{ history|rejectattr('success')|list|length }}</div>
                        <div class="metric-label">失败</div>
                    </div>
                    <div class="col-4">
                        <div class="metric-value text-primary">{{ history|length }}</div>
                        <div class="metric-label">总计</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Purchase History Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-table me-2"></i>购买记录</span>
                <span class="badge bg-primary">{{ history|length }} 条记录</span>
            </div>
            <div class="card-body">
                {% if history %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>产品</th>
                                <th>数量</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>模式</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in history %}
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ record.timestamp.strftime('%Y-%m-%d') if record.timestamp else 'N/A' }}</div>
                                    <small class="text-muted">{{ record.timestamp.strftime('%H:%M:%S') if record.timestamp else '' }}</small>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-gift text-primary me-2"></i>
                                        <span>{{ record.product_name or 'Unknown' }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ record.quantity or 0 }}</span>
                                </td>
                                <td>
                                    <div class="fw-bold">{{ record.currency or '$' }}{{ record.total_amount or 0 }}</div>
                                    {% if record.unit_price %}
                                    <small class="text-muted">单价: {{ record.currency or '$' }}{{ record.unit_price }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.success %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>成功
                                    </span>
                                    {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>失败
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if record.dry_run %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-flask me-1"></i>测试
                                    </span>
                                    {% else %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-credit-card me-1"></i>实际
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewDetails('{{ loop.index0 }}')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if not record.success %}
                                        <button class="btn btn-outline-warning" onclick="retryPurchase('{{ record.product_name }}', {{ record.quantity }})">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无购买记录</h5>
                    <p class="text-muted">在选定的时间范围内没有找到购买记录</p>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>开始购买
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Purchase Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>购买详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="details-content">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Retry Confirmation Modal -->
<div class="modal fade" id="retryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-redo me-2"></i>重试购买
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要重试购买以下产品吗？</p>
                <div class="alert alert-info">
                    <strong>产品:</strong> <span id="retry-product"></span><br>
                    <strong>数量:</strong> <span id="retry-quantity"></span>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    请确保机器人正在运行且配置正确
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmRetry()">
                    <i class="fas fa-redo me-1"></i>确认重试
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Store history data for JavaScript access
    const historyData = {{ history|tojson }};
    let currentRetryProduct = '';
    let currentRetryQuantity = 0;

    function viewDetails(index) {
        const record = historyData[index];
        if (!record) return;
        
        const detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td><strong>产品名称:</strong></td><td>${record.product_name || 'N/A'}</td></tr>
                        <tr><td><strong>购买数量:</strong></td><td>${record.quantity || 0}</td></tr>
                        <tr><td><strong>单价:</strong></td><td>${record.currency || '$'}${record.unit_price || 0}</td></tr>
                        <tr><td><strong>总金额:</strong></td><td>${record.currency || '$'}${record.total_amount || 0}</td></tr>
                        <tr><td><strong>购买时间:</strong></td><td>${record.timestamp || 'N/A'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>状态信息</h6>
                    <table class="table table-sm">
                        <tr><td><strong>购买状态:</strong></td><td>
                            ${record.success ? '<span class="badge bg-success">成功</span>' : '<span class="badge bg-danger">失败</span>'}
                        </td></tr>
                        <tr><td><strong>运行模式:</strong></td><td>
                            ${record.dry_run ? '<span class="badge bg-warning">测试模式</span>' : '<span class="badge bg-info">实际购买</span>'}
                        </td></tr>
                        <tr><td><strong>订单ID:</strong></td><td>${record.order_id || 'N/A'}</td></tr>
                        <tr><td><strong>交易ID:</strong></td><td>${record.transaction_id || 'N/A'}</td></tr>
                    </table>
                </div>
            </div>
            ${record.error_message ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>错误信息</h6>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${record.error_message}
                        </div>
                    </div>
                </div>
            ` : ''}
            ${record.notes ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>备注</h6>
                        <div class="alert alert-info">
                            ${record.notes}
                        </div>
                    </div>
                </div>
            ` : ''}
        `;
        
        document.getElementById('details-content').innerHTML = detailsHtml;
        const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
        modal.show();
    }

    function retryPurchase(productName, quantity) {
        currentRetryProduct = productName;
        currentRetryQuantity = quantity;
        
        document.getElementById('retry-product').textContent = productName;
        document.getElementById('retry-quantity').textContent = quantity;
        
        const modal = new bootstrap.Modal(document.getElementById('retryModal'));
        modal.show();
    }

    function confirmRetry() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('retryModal'));
        modal.hide();
        
        fetch('/api/purchase', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_name: currentRetryProduct,
                quantity: currentRetryQuantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', data.error);
            } else {
                showAlert('info', '重试购买已启动，请查看仪表板了解进度');
            }
        })
        .catch(error => {
            showAlert('danger', '重试失败: ' + error.message);
        });
    }

    function exportHistory() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');
        
        const exportUrl = '/api/export_history?' + params.toString();
        
        // Create a temporary link to download the file
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `purchase_history_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showAlert('success', '历史记录导出已开始');
    }

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid.py-4');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Auto-refresh every 60 seconds
    setInterval(() => {
        window.location.reload();
    }, 60000);
</script>
{% endblock %}
