"""
Configuration management for the GPT Plus purchase bot.
Handles secure credential storage and bot settings.
"""

import os
import logging
from typing import Optional
from pathlib import Path
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv
import keyring

# Load environment variables from .env file
load_dotenv()

class BotConfig(BaseModel):
    """Configuration model for the purchase bot."""
    
    # Authentication
    email: str = Field(..., description="Login email address")
    password: str = Field(..., description="Login password")
    
    # Purchase settings
    default_quantity: int = Field(default=1, ge=1, le=100, description="Default quantity to purchase")
    max_quantity: int = Field(default=5, ge=1, le=100, description="Maximum allowed quantity per purchase")
    preferred_product: str = Field(default="GPT-PLUS", description="Preferred product type")
    
    # Bot behavior
    dry_run: bool = Field(default=True, description="Run in dry-run mode (no actual purchases)")
    request_delay: float = Field(default=2.0, ge=0.5, le=10.0, description="Delay between requests in seconds")
    max_retries: int = Field(default=3, ge=1, le=10, description="Maximum retry attempts")
    timeout: int = Field(default=30, ge=5, le=120, description="Request timeout in seconds")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: str = Field(default="bot.log", description="Log file path")
    
    # Safety features
    enable_purchase_confirmation: bool = Field(default=True, description="Require confirmation before purchases")
    max_daily_purchases: int = Field(default=10, ge=1, le=100, description="Maximum purchases per day")
    
    # Website settings
    base_url: str = Field(default="https://gptpluscz.com", description="Base website URL")
    user_agent: str = Field(
        default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        description="User agent string"
    )
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'log_level must be one of {valid_levels}')
        return v.upper()
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v or '.' not in v:
            raise ValueError('Invalid email format')
        return v.lower()
    
    class Config:
        env_prefix = 'GPTPLUS_'
        case_sensitive = False


class ConfigManager:
    """Manages configuration loading and credential security."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or ".env"
        self.keyring_service = "gptplus_bot"
        self._config: Optional[BotConfig] = None
    
    def load_config(self) -> BotConfig:
        """Load configuration from environment variables and secure storage."""
        if self._config is not None:
            return self._config
        
        # Load from environment variables
        config_data = {}
        
        # Authentication - try keyring first, then environment
        email = os.getenv('GPTPLUS_EMAIL')
        if email:
            config_data['email'] = email
            
            # Try to get password from keyring
            password = self._get_password_from_keyring(email)
            if not password:
                password = os.getenv('GPTPLUS_PASSWORD')
                if password:
                    # Store in keyring for future use
                    self._store_password_in_keyring(email, password)
            
            if password:
                config_data['password'] = password
        
        # Purchase settings
        if os.getenv('DEFAULT_QUANTITY'):
            config_data['default_quantity'] = int(os.getenv('DEFAULT_QUANTITY'))
        if os.getenv('MAX_QUANTITY'):
            config_data['max_quantity'] = int(os.getenv('MAX_QUANTITY'))
        if os.getenv('PREFERRED_PRODUCT'):
            config_data['preferred_product'] = os.getenv('PREFERRED_PRODUCT')
        
        # Bot behavior
        if os.getenv('DRY_RUN'):
            config_data['dry_run'] = os.getenv('DRY_RUN').lower() in ('true', '1', 'yes')
        if os.getenv('REQUEST_DELAY'):
            config_data['request_delay'] = float(os.getenv('REQUEST_DELAY'))
        if os.getenv('MAX_RETRIES'):
            config_data['max_retries'] = int(os.getenv('MAX_RETRIES'))
        if os.getenv('TIMEOUT'):
            config_data['timeout'] = int(os.getenv('TIMEOUT'))
        
        # Logging
        if os.getenv('LOG_LEVEL'):
            config_data['log_level'] = os.getenv('LOG_LEVEL')
        if os.getenv('LOG_FILE'):
            config_data['log_file'] = os.getenv('LOG_FILE')
        
        # Safety features
        if os.getenv('ENABLE_PURCHASE_CONFIRMATION'):
            config_data['enable_purchase_confirmation'] = os.getenv('ENABLE_PURCHASE_CONFIRMATION').lower() in ('true', '1', 'yes')
        if os.getenv('MAX_DAILY_PURCHASES'):
            config_data['max_daily_purchases'] = int(os.getenv('MAX_DAILY_PURCHASES'))
        
        try:
            self._config = BotConfig(**config_data)
            return self._config
        except Exception as e:
            raise ValueError(f"Configuration error: {e}")
    
    def _get_password_from_keyring(self, email: str) -> Optional[str]:
        """Retrieve password from system keyring."""
        try:
            return keyring.get_password(self.keyring_service, email)
        except Exception:
            return None
    
    def _store_password_in_keyring(self, email: str, password: str) -> bool:
        """Store password in system keyring."""
        try:
            keyring.set_password(self.keyring_service, email, password)
            return True
        except Exception:
            return False
    
    def update_credentials(self, email: str, password: str) -> bool:
        """Update stored credentials."""
        try:
            self._store_password_in_keyring(email, password)
            if self._config:
                self._config.email = email
                self._config.password = password
            return True
        except Exception:
            return False
    
    def clear_credentials(self, email: str) -> bool:
        """Clear stored credentials."""
        try:
            keyring.delete_password(self.keyring_service, email)
            return True
        except Exception:
            return False
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """Validate the current configuration."""
        errors = []
        
        try:
            config = self.load_config()
            
            # Check required fields
            if not config.email:
                errors.append("Email is required")
            if not config.password:
                errors.append("Password is required")
            
            # Check logical constraints
            if config.default_quantity > config.max_quantity:
                errors.append("Default quantity cannot exceed max quantity")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            errors.append(f"Configuration validation error: {e}")
            return False, errors


# Global configuration instance
config_manager = ConfigManager()


def get_config() -> BotConfig:
    """Get the current bot configuration."""
    return config_manager.load_config()


def setup_logging(config: BotConfig) -> logging.Logger:
    """Set up logging based on configuration."""
    # Create logs directory if it doesn't exist
    log_path = Path(config.log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, config.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger('gptplus_bot')
    logger.info(f"Logging initialized - Level: {config.log_level}, File: {config.log_file}")
    
    return logger
